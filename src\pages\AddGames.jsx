import React, { useState, useEffect } from "react";
import { useAuth } from "../context/AuthContext";

const AddGames = () => {
  const { user, addGameToUser } = useAuth();
  const [activeTab, setActiveTab] = useState("steam");
  const [searchTerm, setSearchTerm] = useState("");
  const [searchResults, setSearchResults] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedGame, setSelectedGame] = useState(null);

  // Estados para adição manual
  const [manualGame, setManualGame] = useState({
    name: "",
    developer: "",
    publisher: "",
    genre: "",
    releaseDate: "",
    description: "",
    imageUrl: "",
  });

  // Função para buscar jogos na Steam (simulada)
  const searchSteamGames = async (query) => {
    if (!query.trim()) return;

    setLoading(true);
    try {
      // Simulação de dados da Steam API
      const mockResults = [
        {
          appid: 1091500,
          name: "Cyberpunk 2077",
          developer: "CD PROJEKT RED",
          publisher: "CD PROJEKT RED",
          release_date: "2020-12-10",
          price: "R$ 199,90",
          image:
            "https://cdn.akamai.steamstatic.com/steam/apps/1091500/header.jpg",
          description:
            "Cyberpunk 2077 é um RPG de ação e aventura em mundo aberto que se passa em Night City.",
          tags: ["RPG", "Mundo Aberto", "Cyberpunk", "Ação"],
          rating: 4.2,
        },
        {
          appid: 292030,
          name: "The Witcher 3: Wild Hunt",
          developer: "CD PROJEKT RED",
          publisher: "CD PROJEKT RED",
          release_date: "2015-05-19",
          price: "R$ 39,99",
          image:
            "https://cdn.akamai.steamstatic.com/steam/apps/292030/header.jpg",
          description:
            "The Witcher 3: Wild Hunt é um RPG de mundo aberto de nova geração.",
          tags: ["RPG", "Mundo Aberto", "Fantasia", "História Rica"],
          rating: 4.8,
        },
        {
          appid: 1174180,
          name: "Red Dead Redemption 2",
          developer: "Rockstar Games",
          publisher: "Rockstar Games",
          release_date: "2019-12-05",
          price: "R$ 299,90",
          image:
            "https://cdn.akamai.steamstatic.com/steam/apps/1174180/header.jpg",
          description:
            "Red Dead Redemption 2 é um épico de ação e aventura ambientado no coração da América.",
          tags: ["Ação", "Aventura", "Mundo Aberto", "Western"],
          rating: 4.6,
        },
        {
          appid: 271590,
          name: "Grand Theft Auto V",
          developer: "Rockstar North",
          publisher: "Rockstar Games",
          release_date: "2015-04-14",
          price: "R$ 89,90",
          image:
            "https://cdn.akamai.steamstatic.com/steam/apps/271590/header.jpg",
          description:
            "Grand Theft Auto V para PC oferece aos jogadores a opção de explorar o mundo premiado e enorme de Los Santos.",
          tags: ["Ação", "Aventura", "Mundo Aberto", "Crime"],
          rating: 4.4,
        },
        {
          appid: 544330,
          name: "Nine Parchments",
          developer: "Frozenbyte",
          publisher: "Frozenbyte",
          release_date: "2017-12-05",
          price: "R$ 37,99",
          image:
            "https://cdn.akamai.steamstatic.com/steam/apps/544330/header.jpg",
          description:
            "Nine Parchments é um jogo de ação cooperativo de fantasia para até 4 jogadores.",
          tags: ["Ação", "Cooperativo", "Fantasia", "Magia"],
          rating: 4.1,
        },
        {
          appid: 1623730,
          name: "Palworld",
          developer: "Pocketpair",
          publisher: "Pocketpair",
          release_date: "2024-01-19",
          price: "R$ 89,90",
          image:
            "https://cdn.akamai.steamstatic.com/steam/apps/1623730/header.jpg",
          description:
            "Palworld é um jogo de aventura multiplayer onde você pode coletar criaturas misteriosas chamadas Pals.",
          tags: ["Aventura", "Multiplayer", "Sobrevivência", "Mundo Aberto"],
          rating: 4.3,
        },
        {
          appid: 268910,
          name: "Cuphead",
          developer: "StudioMDHR",
          publisher: "StudioMDHR",
          release_date: "2017-09-29",
          price: "R$ 37,99",
          image:
            "https://cdn.akamai.steamstatic.com/steam/apps/268910/header.jpg",
          description:
            "Cuphead é um jogo de ação clássico com foco em batalhas de chefe inspirado em desenhos animados dos anos 1930.",
          tags: ["Ação", "Plataforma", "Difícil", "Indie"],
          rating: 4.7,
        },
        {
          appid: 730,
          name: "Counter-Strike 2",
          developer: "Valve",
          publisher: "Valve",
          release_date: "2023-09-27",
          price: "Gratuito",
          image: "https://cdn.akamai.steamstatic.com/steam/apps/730/header.jpg",
          description:
            "Counter-Strike 2 é a maior atualização técnica da história do Counter-Strike.",
          tags: ["FPS", "Competitivo", "Multiplayer", "Tático"],
          rating: 4.5,
        },
        {
          appid: 570,
          name: "Dota 2",
          developer: "Valve",
          publisher: "Valve",
          release_date: "2013-07-09",
          price: "Gratuito",
          image: "https://cdn.akamai.steamstatic.com/steam/apps/570/header.jpg",
          description:
            "Dota 2 é um jogo MOBA competitivo jogado por milhões de pessoas ao redor do mundo.",
          tags: ["MOBA", "Estratégia", "Competitivo", "Multiplayer"],
          rating: 4.3,
        },
        {
          appid: 1086940,
          name: "Baldur's Gate 3",
          developer: "Larian Studios",
          publisher: "Larian Studios",
          release_date: "2023-08-03",
          price: "R$ 199,90",
          image:
            "https://cdn.akamai.steamstatic.com/steam/apps/1086940/header.jpg",
          description:
            "Baldur's Gate 3 é um RPG de próxima geração ambientado no universo de Dungeons & Dragons.",
          tags: ["RPG", "Estratégia", "Fantasia", "História Rica"],
          rating: 4.9,
        },
        {
          appid: 1245620,
          name: "ELDEN RING",
          developer: "FromSoftware",
          publisher: "Bandai Namco",
          release_date: "2022-02-25",
          price: "R$ 249,90",
          image:
            "https://cdn.akamai.steamstatic.com/steam/apps/1245620/header.jpg",
          description:
            "ELDEN RING é um RPG de ação e fantasia desenvolvido pela FromSoftware.",
          tags: ["RPG", "Ação", "Souls-like", "Mundo Aberto"],
          rating: 4.6,
        },
        {
          appid: 1172470,
          name: "Apex Legends",
          developer: "Respawn Entertainment",
          publisher: "Electronic Arts",
          release_date: "2020-11-04",
          price: "Gratuito",
          image:
            "https://cdn.akamai.steamstatic.com/steam/apps/1172470/header.jpg",
          description:
            "Apex Legends é um jogo battle royale gratuito onde lendas competem por glória, fama e fortuna.",
          tags: ["Battle Royale", "FPS", "Gratuito", "Multiplayer"],
          rating: 4.2,
        },
        {
          appid: 1966720,
          name: "Spider-Man Remastered",
          developer: "Insomniac Games",
          publisher: "PlayStation PC LLC",
          release_date: "2022-08-12",
          price: "R$ 199,90",
          image:
            "https://cdn.akamai.steamstatic.com/steam/apps/1966720/header.jpg",
          description:
            "Em Spider-Man Remastered, os mundos de Peter Parker e Spider-Man colidem em uma história original.",
          tags: ["Ação", "Aventura", "Super-herói", "Mundo Aberto"],
          rating: 4.7,
        },
        // Jogos de Ação/Aventura
        {
          appid: 489830,
          name: "The Elder Scrolls V: Skyrim Special Edition",
          developer: "Bethesda Game Studios",
          publisher: "Bethesda Softworks",
          release_date: "2016-10-28",
          price: "R$ 79,99",
          image:
            "https://cdn.akamai.steamstatic.com/steam/apps/489830/header.jpg",
          description:
            "Skyrim Special Edition traz o épico jogo de fantasia com gráficos e efeitos remasterizados.",
          tags: ["RPG", "Mundo Aberto", "Fantasia", "Moddable"],
          rating: 4.6,
        },
        {
          appid: 377160,
          name: "Fallout 4",
          developer: "Bethesda Game Studios",
          publisher: "Bethesda Softworks",
          release_date: "2015-11-10",
          price: "R$ 59,99",
          image:
            "https://cdn.akamai.steamstatic.com/steam/apps/377160/header.jpg",
          description: "Fallout 4 é um RPG pós-apocalíptico em mundo aberto.",
          tags: ["RPG", "Pós-Apocalíptico", "Mundo Aberto", "Crafting"],
          rating: 4.3,
        },
        {
          appid: 1174180,
          name: "Red Dead Redemption 2",
          developer: "Rockstar Games",
          publisher: "Rockstar Games",
          release_date: "2019-12-05",
          price: "R$ 299,90",
          image:
            "https://cdn.akamai.steamstatic.com/steam/apps/1174180/header.jpg",
          description:
            "Red Dead Redemption 2 é um épico de ação e aventura ambientado no coração da América.",
          tags: ["Ação", "Aventura", "Mundo Aberto", "Western"],
          rating: 4.6,
        },
        // Jogos de Tiro (FPS)
        {
          appid: 1938090,
          name: "Call of Duty: Modern Warfare III",
          developer: "Sledgehammer Games",
          publisher: "Activision",
          release_date: "2023-11-10",
          price: "R$ 299,90",
          image:
            "https://cdn.akamai.steamstatic.com/steam/apps/1938090/header.jpg",
          description:
            "Call of Duty: Modern Warfare III é um jogo de tiro em primeira pessoa.",
          tags: ["FPS", "Ação", "Multiplayer", "Guerra"],
          rating: 4.0,
        },
        {
          appid: 1517290,
          name: "Battlefield 2042",
          developer: "DICE",
          publisher: "Electronic Arts",
          release_date: "2021-11-19",
          price: "R$ 199,90",
          image:
            "https://cdn.akamai.steamstatic.com/steam/apps/1517290/header.jpg",
          description:
            "Battlefield 2042 é um jogo de tiro em primeira pessoa que marca o retorno da icônica guerra total da franquia.",
          tags: ["FPS", "Multiplayer", "Guerra", "Ação"],
          rating: 3.8,
        },
        {
          appid: 1172470,
          name: "Apex Legends",
          developer: "Respawn Entertainment",
          publisher: "Electronic Arts",
          release_date: "2020-11-04",
          price: "Gratuito",
          image:
            "https://cdn.akamai.steamstatic.com/steam/apps/1172470/header.jpg",
          description:
            "Apex Legends é um jogo battle royale gratuito onde lendas competem por glória, fama e fortuna.",
          tags: ["Battle Royale", "FPS", "Gratuito", "Multiplayer"],
          rating: 4.2,
        },
        {
          appid: 578080,
          name: "PUBG: BATTLEGROUNDS",
          developer: "KRAFTON, Inc.",
          publisher: "KRAFTON, Inc.",
          release_date: "2017-12-21",
          price: "Gratuito",
          image:
            "https://cdn.akamai.steamstatic.com/steam/apps/578080/header.jpg",
          description:
            "PUBG é um jogo battle royale onde 100 jogadores lutam para ser o último sobrevivente.",
          tags: ["Battle Royale", "FPS", "Multiplayer", "Sobrevivência"],
          rating: 4.1,
        },
        {
          appid: 1938090,
          name: "Valorant",
          developer: "Riot Games",
          publisher: "Riot Games",
          release_date: "2020-06-02",
          price: "Gratuito",
          image:
            "https://cdn.akamai.steamstatic.com/steam/apps/1172470/header.jpg",
          description: "Valorant é um FPS tático 5v5 com personagens únicos.",
          tags: ["FPS", "Tático", "Competitivo", "Gratuito"],
          rating: 4.3,
        },
        // Jogos de Estratégia
        {
          appid: 570,
          name: "Dota 2",
          developer: "Valve",
          publisher: "Valve",
          release_date: "2013-07-09",
          price: "Gratuito",
          image: "https://cdn.akamai.steamstatic.com/steam/apps/570/header.jpg",
          description:
            "Dota 2 é um jogo MOBA competitivo jogado por milhões de pessoas ao redor do mundo.",
          tags: ["MOBA", "Estratégia", "Competitivo", "Multiplayer"],
          rating: 4.3,
        },
        {
          appid: 1091500,
          name: "League of Legends",
          developer: "Riot Games",
          publisher: "Riot Games",
          release_date: "2009-10-27",
          price: "Gratuito",
          image: "https://cdn.akamai.steamstatic.com/steam/apps/570/header.jpg",
          description: "League of Legends é o MOBA mais popular do mundo.",
          tags: ["MOBA", "Estratégia", "Competitivo", "Gratuito"],
          rating: 4.4,
        },
        {
          appid: 394360,
          name: "Hearts of Iron IV",
          developer: "Paradox Development Studio",
          publisher: "Paradox Interactive",
          release_date: "2016-06-06",
          price: "R$ 89,99",
          image:
            "https://cdn.akamai.steamstatic.com/steam/apps/394360/header.jpg",
          description:
            "Hearts of Iron IV é um jogo de estratégia da Segunda Guerra Mundial.",
          tags: ["Estratégia", "Guerra", "Histórico", "Grand Strategy"],
          rating: 4.5,
        },
        {
          appid: 236850,
          name: "Europa Universalis IV",
          developer: "Paradox Development Studio",
          publisher: "Paradox Interactive",
          release_date: "2013-08-13",
          price: "R$ 89,99",
          image:
            "https://cdn.akamai.steamstatic.com/steam/apps/236850/header.jpg",
          description:
            "Europa Universalis IV é um jogo de estratégia histórica.",
          tags: ["Estratégia", "Histórico", "Grand Strategy", "Diplomacia"],
          rating: 4.6,
        },
        // Jogos Indie
        {
          appid: 268910,
          name: "Cuphead",
          developer: "StudioMDHR",
          publisher: "StudioMDHR",
          release_date: "2017-09-29",
          price: "R$ 37,99",
          image:
            "https://cdn.akamai.steamstatic.com/steam/apps/268910/header.jpg",
          description:
            "Cuphead é um jogo de ação clássico com foco em batalhas de chefe inspirado em desenhos animados dos anos 1930.",
          tags: ["Ação", "Plataforma", "Difícil", "Indie"],
          rating: 4.7,
        },
        {
          appid: 367520,
          name: "Hollow Knight",
          developer: "Team Cherry",
          publisher: "Team Cherry",
          release_date: "2017-02-24",
          price: "R$ 29,99",
          image:
            "https://cdn.akamai.steamstatic.com/steam/apps/367520/header.jpg",
          description:
            "Hollow Knight é um jogo de ação e aventura 2D que se passa em Hallownest.",
          tags: ["Metroidvania", "Indie", "Plataforma", "Atmosférico"],
          rating: 4.8,
        },
        {
          appid: 413150,
          name: "Stardew Valley",
          developer: "ConcernedApe",
          publisher: "ConcernedApe",
          release_date: "2016-02-26",
          price: "R$ 24,99",
          image:
            "https://cdn.akamai.steamstatic.com/steam/apps/413150/header.jpg",
          description:
            "Stardew Valley é um jogo de simulação de fazenda indie.",
          tags: ["Simulação", "Indie", "Relaxante", "Farming"],
          rating: 4.9,
        },
        {
          appid: 105600,
          name: "Terraria",
          developer: "Re-Logic",
          publisher: "Re-Logic",
          release_date: "2011-05-16",
          price: "R$ 19,99",
          image:
            "https://cdn.akamai.steamstatic.com/steam/apps/105600/header.jpg",
          description: "Terraria é um jogo de aventura sandbox 2D.",
          tags: ["Sandbox", "Aventura", "Crafting", "2D"],
          rating: 4.7,
        },
        // Jogos de Corrida
        {
          appid: 1551360,
          name: "Forza Horizon 5",
          developer: "Playground Games",
          publisher: "Xbox Game Studios",
          release_date: "2021-11-09",
          price: "R$ 249,90",
          image:
            "https://cdn.akamai.steamstatic.com/steam/apps/1551360/header.jpg",
          description:
            "Forza Horizon 5 é um jogo de corrida em mundo aberto ambientado no México.",
          tags: ["Corrida", "Mundo Aberto", "Carros", "Arcade"],
          rating: 4.6,
        },
        {
          appid: 1222670,
          name: "F1 23",
          developer: "Codemasters",
          publisher: "EA Sports",
          release_date: "2023-06-16",
          price: "R$ 299,90",
          image:
            "https://cdn.akamai.steamstatic.com/steam/apps/1222670/header.jpg",
          description: "F1 23 é o jogo oficial da Fórmula 1.",
          tags: ["Corrida", "Simulação", "Esportes", "F1"],
          rating: 4.2,
        },
        // Jogos de Sobrevivência
        {
          appid: 1623730,
          name: "Palworld",
          developer: "Pocketpair",
          publisher: "Pocketpair",
          release_date: "2024-01-19",
          price: "R$ 89,90",
          image:
            "https://cdn.akamai.steamstatic.com/steam/apps/1623730/header.jpg",
          description:
            "Palworld é um jogo de aventura multiplayer onde você pode coletar criaturas misteriosas chamadas Pals.",
          tags: ["Aventura", "Multiplayer", "Sobrevivência", "Mundo Aberto"],
          rating: 4.3,
        },
        {
          appid: 252490,
          name: "Rust",
          developer: "Facepunch Studios",
          publisher: "Facepunch Studios",
          release_date: "2018-02-08",
          price: "R$ 89,99",
          image:
            "https://cdn.akamai.steamstatic.com/steam/apps/252490/header.jpg",
          description: "Rust é um jogo de sobrevivência multiplayer.",
          tags: ["Sobrevivência", "Multiplayer", "Crafting", "PvP"],
          rating: 4.1,
        },
        {
          appid: 346110,
          name: "ARK: Survival Evolved",
          developer: "Studio Wildcard",
          publisher: "Studio Wildcard",
          release_date: "2017-08-27",
          price: "R$ 59,99",
          image:
            "https://cdn.akamai.steamstatic.com/steam/apps/346110/header.jpg",
          description:
            "ARK: Survival Evolved é um jogo de sobrevivência com dinossauros.",
          tags: ["Sobrevivência", "Dinossauros", "Crafting", "Multiplayer"],
          rating: 4.0,
        },
        {
          appid: 322330,
          name: "Don't Starve Together",
          developer: "Klei Entertainment",
          publisher: "Klei Entertainment",
          release_date: "2016-04-21",
          price: "R$ 29,99",
          image:
            "https://cdn.akamai.steamstatic.com/steam/apps/322330/header.jpg",
          description:
            "Don't Starve Together é um jogo de sobrevivência cooperativo.",
          tags: ["Sobrevivência", "Cooperativo", "Indie", "Tim Burton"],
          rating: 4.4,
        },
        // Jogos de Plataforma
        {
          appid: 214770,
          name: "Mark of the Ninja: Remastered",
          developer: "Klei Entertainment",
          publisher: "Klei Entertainment",
          release_date: "2018-10-09",
          price: "R$ 39,99",
          image:
            "https://cdn.akamai.steamstatic.com/steam/apps/214770/header.jpg",
          description: "Mark of the Ninja é um jogo de stealth 2D.",
          tags: ["Stealth", "Plataforma", "Ninja", "2D"],
          rating: 4.6,
        },
        {
          appid: 200260,
          name: "Batman: Arkham City GOTY",
          developer: "Rocksteady Studios",
          publisher: "Warner Bros. Games",
          release_date: "2011-11-22",
          price: "R$ 39,99",
          image:
            "https://cdn.akamai.steamstatic.com/steam/apps/200260/header.jpg",
          description:
            "Batman: Arkham City é um jogo de ação e aventura com o Batman.",
          tags: ["Ação", "Super-herói", "Stealth", "Aventura"],
          rating: 4.7,
        },
        // Jogos de Simulação
        {
          appid: 1966720,
          name: "Spider-Man Remastered",
          developer: "Insomniac Games",
          publisher: "PlayStation PC LLC",
          release_date: "2022-08-12",
          price: "R$ 199,90",
          image:
            "https://cdn.akamai.steamstatic.com/steam/apps/1966720/header.jpg",
          description:
            "Em Spider-Man Remastered, os mundos de Peter Parker e Spider-Man colidem em uma história original.",
          tags: ["Ação", "Aventura", "Super-herói", "Mundo Aberto"],
          rating: 4.7,
        },
        {
          appid: 431960,
          name: "Wallpaper Engine",
          developer: "Kristjan Skutta",
          publisher: "Kristjan Skutta",
          release_date: "2016-11-02",
          price: "R$ 9,99",
          image:
            "https://cdn.akamai.steamstatic.com/steam/apps/431960/header.jpg",
          description:
            "Wallpaper Engine permite usar papéis de parede animados e interativos.",
          tags: ["Utilidade", "Personalização", "Software", "Wallpaper"],
          rating: 4.8,
        },
        // Mais jogos populares
        {
          appid: 544330,
          name: "Nine Parchments",
          developer: "Frozenbyte",
          publisher: "Frozenbyte",
          release_date: "2017-12-05",
          price: "R$ 37,99",
          image:
            "https://cdn.akamai.steamstatic.com/steam/apps/544330/header.jpg",
          description:
            "Nine Parchments é um jogo de ação cooperativo de fantasia para até 4 jogadores.",
          tags: ["Ação", "Cooperativo", "Fantasia", "Magia"],
          rating: 4.1,
        },
        {
          appid: 1245620,
          name: "ELDEN RING",
          developer: "FromSoftware",
          publisher: "Bandai Namco",
          release_date: "2022-02-25",
          price: "R$ 249,90",
          image:
            "https://cdn.akamai.steamstatic.com/steam/apps/1245620/header.jpg",
          description:
            "ELDEN RING é um RPG de ação e fantasia desenvolvido pela FromSoftware.",
          tags: ["RPG", "Ação", "Souls-like", "Mundo Aberto"],
          rating: 4.6,
        },
        {
          appid: 1086940,
          name: "Baldur's Gate 3",
          developer: "Larian Studios",
          publisher: "Larian Studios",
          release_date: "2023-08-03",
          price: "R$ 199,90",
          image:
            "https://cdn.akamai.steamstatic.com/steam/apps/1086940/header.jpg",
          description:
            "Baldur's Gate 3 é um RPG de próxima geração ambientado no universo de Dungeons & Dragons.",
          tags: ["RPG", "Estratégia", "Fantasia", "História Rica"],
          rating: 4.9,
        },
        {
          appid: 1174180,
          name: "Minecraft",
          developer: "Mojang Studios",
          publisher: "Microsoft Studios",
          release_date: "2011-11-18",
          price: "R$ 89,90",
          image:
            "https://cdn.akamai.steamstatic.com/steam/apps/1174180/header.jpg",
          description:
            "Minecraft é um jogo sandbox onde você pode construir qualquer coisa que imaginar.",
          tags: ["Sandbox", "Crafting", "Sobrevivência", "Criativo"],
          rating: 4.8,
        },
        {
          appid: 730,
          name: "Counter-Strike 2",
          developer: "Valve",
          publisher: "Valve",
          release_date: "2023-09-27",
          price: "Gratuito",
          image: "https://cdn.akamai.steamstatic.com/steam/apps/730/header.jpg",
          description:
            "Counter-Strike 2 é a maior atualização técnica da história do Counter-Strike.",
          tags: ["FPS", "Competitivo", "Multiplayer", "Tático"],
          rating: 4.5,
        },
        // Mais jogos AAA
        {
          appid: 1174180,
          name: "God of War",
          developer: "Santa Monica Studio",
          publisher: "PlayStation PC LLC",
          release_date: "2022-01-14",
          price: "R$ 199,90",
          image:
            "https://cdn.akamai.steamstatic.com/steam/apps/1593500/header.jpg",
          description:
            "God of War é um jogo de ação e aventura que segue Kratos e seu filho Atreus.",
          tags: ["Ação", "Aventura", "Mitologia", "História Rica"],
          rating: 4.8,
        },
        {
          appid: 1174180,
          name: "Horizon Zero Dawn",
          developer: "Guerrilla Games",
          publisher: "PlayStation PC LLC",
          release_date: "2020-08-07",
          price: "R$ 199,90",
          image:
            "https://cdn.akamai.steamstatic.com/steam/apps/1151640/header.jpg",
          description:
            "Horizon Zero Dawn é um RPG de ação em mundo aberto pós-apocalíptico.",
          tags: ["RPG", "Pós-Apocalíptico", "Mundo Aberto", "Robôs"],
          rating: 4.5,
        },
        {
          appid: 1174180,
          name: "Death Stranding",
          developer: "Kojima Productions",
          publisher: "505 Games",
          release_date: "2020-07-14",
          price: "R$ 199,90",
          image:
            "https://cdn.akamai.steamstatic.com/steam/apps/1190460/header.jpg",
          description:
            "Death Stranding é um jogo de ação desenvolvido por Hideo Kojima.",
          tags: ["Ação", "Aventura", "Sci-Fi", "Kojima"],
          rating: 4.2,
        },
        {
          appid: 1174180,
          name: "Control",
          developer: "Remedy Entertainment",
          publisher: "505 Games",
          release_date: "2020-08-27",
          price: "R$ 99,90",
          image:
            "https://cdn.akamai.steamstatic.com/steam/apps/870780/header.jpg",
          description:
            "Control é um jogo de ação sobrenatural em terceira pessoa.",
          tags: ["Ação", "Sobrenatural", "Sci-Fi", "Mistério"],
          rating: 4.4,
        },
        // Jogos de Horror
        {
          appid: 1174180,
          name: "Resident Evil 4",
          developer: "Capcom",
          publisher: "Capcom",
          release_date: "2023-03-24",
          price: "R$ 249,90",
          image:
            "https://cdn.akamai.steamstatic.com/steam/apps/2050650/header.jpg",
          description:
            "Resident Evil 4 é um remake do clássico jogo de survival horror.",
          tags: ["Horror", "Ação", "Sobrevivência", "Remake"],
          rating: 4.7,
        },
        {
          appid: 1174180,
          name: "Phasmophobia",
          developer: "Kinetic Games",
          publisher: "Kinetic Games",
          release_date: "2020-09-18",
          price: "R$ 29,99",
          image:
            "https://cdn.akamai.steamstatic.com/steam/apps/739630/header.jpg",
          description:
            "Phasmophobia é um jogo de horror cooperativo de investigação paranormal.",
          tags: ["Horror", "Cooperativo", "Investigação", "Fantasmas"],
          rating: 4.3,
        },
        // Jogos de Esporte
        {
          appid: 1174180,
          name: "FIFA 24",
          developer: "EA Sports",
          publisher: "Electronic Arts",
          release_date: "2023-09-29",
          price: "R$ 299,90",
          image:
            "https://cdn.akamai.steamstatic.com/steam/apps/2195250/header.jpg",
          description: "FIFA 24 é o jogo de futebol mais realista do mundo.",
          tags: ["Esportes", "Futebol", "Simulação", "Multiplayer"],
          rating: 4.1,
        },
        {
          appid: 1174180,
          name: "Rocket League",
          developer: "Psyonix",
          publisher: "Psyonix",
          release_date: "2015-07-07",
          price: "Gratuito",
          image:
            "https://cdn.akamai.steamstatic.com/steam/apps/252950/header.jpg",
          description:
            "Rocket League é um jogo de futebol com carros movidos a foguete.",
          tags: ["Esportes", "Carros", "Multiplayer", "Competitivo"],
          rating: 4.6,
        },
        // Jogos de Luta
        {
          appid: 1174180,
          name: "Street Fighter 6",
          developer: "Capcom",
          publisher: "Capcom",
          release_date: "2023-06-02",
          price: "R$ 299,90",
          image:
            "https://cdn.akamai.steamstatic.com/steam/apps/1364780/header.jpg",
          description:
            "Street Fighter 6 é o mais novo jogo da icônica série de luta.",
          tags: ["Luta", "Competitivo", "Arcade", "Multiplayer"],
          rating: 4.5,
        },
        {
          appid: 1174180,
          name: "Tekken 8",
          developer: "Bandai Namco",
          publisher: "Bandai Namco",
          release_date: "2024-01-26",
          price: "R$ 299,90",
          image:
            "https://cdn.akamai.steamstatic.com/steam/apps/1778820/header.jpg",
          description: "Tekken 8 continua a saga da família Mishima.",
          tags: ["Luta", "3D", "Competitivo", "História"],
          rating: 4.4,
        },
        // Jogos Clássicos
        {
          appid: 1174180,
          name: "Half-Life 2",
          developer: "Valve",
          publisher: "Valve",
          release_date: "2004-11-16",
          price: "R$ 19,99",
          image: "https://cdn.akamai.steamstatic.com/steam/apps/220/header.jpg",
          description:
            "Half-Life 2 é um dos jogos mais influentes de todos os tempos.",
          tags: ["FPS", "Sci-Fi", "Clássico", "História"],
          rating: 4.9,
        },
        {
          appid: 1174180,
          name: "Portal 2",
          developer: "Valve",
          publisher: "Valve",
          release_date: "2011-04-18",
          price: "R$ 19,99",
          image: "https://cdn.akamai.steamstatic.com/steam/apps/620/header.jpg",
          description: "Portal 2 é um jogo de puzzle em primeira pessoa.",
          tags: ["Puzzle", "Sci-Fi", "Cooperativo", "Humor"],
          rating: 4.9,
        },
        // Jogos de Mundo Aberto
        {
          appid: 1174180,
          name: "Assassin's Creed Valhalla",
          developer: "Ubisoft Montreal",
          publisher: "Ubisoft",
          release_date: "2020-11-10",
          price: "R$ 249,90",
          image:
            "https://cdn.akamai.steamstatic.com/steam/apps/2208920/header.jpg",
          description: "Assassin's Creed Valhalla leva você à era dos vikings.",
          tags: ["Ação", "Aventura", "Mundo Aberto", "Vikings"],
          rating: 4.2,
        },
        {
          appid: 1174180,
          name: "Watch Dogs: Legion",
          developer: "Ubisoft Toronto",
          publisher: "Ubisoft",
          release_date: "2020-10-29",
          price: "R$ 199,90",
          image:
            "https://cdn.akamai.steamstatic.com/steam/apps/2239550/header.jpg",
          description: "Watch Dogs: Legion se passa em uma Londres pós-Brexit.",
          tags: ["Ação", "Hacking", "Mundo Aberto", "Cyberpunk"],
          rating: 3.9,
        },
        // Jogos de Simulação
        {
          appid: 1174180,
          name: "Cities: Skylines",
          developer: "Colossal Order",
          publisher: "Paradox Interactive",
          release_date: "2015-03-10",
          price: "R$ 59,99",
          image:
            "https://cdn.akamai.steamstatic.com/steam/apps/255710/header.jpg",
          description:
            "Cities: Skylines é um simulador de construção de cidades.",
          tags: ["Simulação", "Construção", "Estratégia", "Cidade"],
          rating: 4.6,
        },
        {
          appid: 1174180,
          name: "The Sims 4",
          developer: "Maxis",
          publisher: "Electronic Arts",
          release_date: "2014-09-02",
          price: "Gratuito",
          image:
            "https://cdn.akamai.steamstatic.com/steam/apps/1222670/header.jpg",
          description:
            "The Sims 4 é um simulador de vida onde você controla pessoas virtuais.",
          tags: ["Simulação", "Vida", "Casual", "Criativo"],
          rating: 4.3,
        },
        // Jogos Magicka e outros
        {
          appid: 42910,
          name: "Magicka",
          developer: "Arrowhead Game Studios",
          publisher: "Paradox Interactive",
          release_date: "2011-01-25",
          price: "R$ 19,99",
          image:
            "https://cdn.akamai.steamstatic.com/steam/apps/42910/header.jpg",
          description:
            "Magicka é um jogo de ação e aventura baseado em magia cooperativo para até 4 jogadores.",
          tags: ["Ação", "Cooperativo", "Magia", "Humor"],
          rating: 4.2,
        },
        {
          appid: 238370,
          name: "Magicka 2",
          developer: "Pieces Interactive",
          publisher: "Paradox Interactive",
          release_date: "2015-05-26",
          price: "R$ 29,99",
          image:
            "https://cdn.akamai.steamstatic.com/steam/apps/238370/header.jpg",
          description:
            "Magicka 2 é a sequência do aclamado jogo de ação cooperativo baseado em magia.",
          tags: ["Ação", "Cooperativo", "Magia", "Fantasia"],
          rating: 4.1,
        },
        {
          appid: 1046930,
          name: "Popcorn",
          developer: "Laush Dmitriy Sergeevich",
          publisher: "Laush Dmitriy Sergeevich",
          release_date: "2019-08-15",
          price: "R$ 9,99",
          image:
            "https://cdn.akamai.steamstatic.com/steam/apps/1046930/header.jpg",
          description:
            "Popcorn é um jogo casual e relaxante de quebra-cabeças.",
          tags: ["Casual", "Puzzle", "Relaxante", "Indie"],
          rating: 4.0,
        },
        // Mais jogos cooperativos similares ao Magicka
        {
          appid: 620980,
          name: "Overcooked! 2",
          developer: "Ghost Town Games",
          publisher: "Team17",
          release_date: "2018-08-07",
          price: "R$ 49,99",
          image:
            "https://cdn.akamai.steamstatic.com/steam/apps/620980/header.jpg",
          description:
            "Overcooked! 2 é um jogo de culinária cooperativo caótico para até 4 jogadores.",
          tags: ["Cooperativo", "Casual", "Culinária", "Caótico"],
          rating: 4.6,
        },
        {
          appid: 448510,
          name: "Overcooked!",
          developer: "Ghost Town Games",
          publisher: "Team17",
          release_date: "2016-08-03",
          price: "R$ 34,99",
          image:
            "https://cdn.akamai.steamstatic.com/steam/apps/448510/header.jpg",
          description:
            "Overcooked! é um jogo de culinária cooperativo onde você deve trabalhar em equipe.",
          tags: ["Cooperativo", "Casual", "Culinária", "Local"],
          rating: 4.5,
        },
        {
          appid: 394690,
          name: "Tower Unite",
          developer: "PixelTail Games",
          publisher: "PixelTail Games",
          release_date: "2016-04-08",
          price: "R$ 29,99",
          image:
            "https://cdn.akamai.steamstatic.com/steam/apps/394690/header.jpg",
          description:
            "Tower Unite é um lobby social multiplayer com vários minijogos.",
          tags: ["Social", "Multiplayer", "Minijogos", "Casual"],
          rating: 4.3,
        },
        {
          appid: 251570,
          name: "7 Days to Die",
          developer: "The Fun Pimps",
          publisher: "The Fun Pimps",
          release_date: "2013-12-13",
          price: "R$ 49,99",
          image:
            "https://cdn.akamai.steamstatic.com/steam/apps/251570/header.jpg",
          description:
            "7 Days to Die é um jogo de sobrevivência com zumbis em mundo aberto.",
          tags: ["Sobrevivência", "Zumbis", "Crafting", "Cooperativo"],
          rating: 4.0,
        },
        {
          appid: 892970,
          name: "Valheim",
          developer: "Iron Gate AB",
          publisher: "Coffee Stain Publishing",
          release_date: "2021-02-02",
          price: "R$ 37,99",
          image:
            "https://cdn.akamai.steamstatic.com/steam/apps/892970/header.jpg",
          description:
            "Valheim é um jogo de sobrevivência cooperativo inspirado na cultura viking.",
          tags: ["Sobrevivência", "Vikings", "Cooperativo", "Crafting"],
          rating: 4.7,
        },
        {
          appid: 1086940,
          name: "It Takes Two",
          developer: "Hazelight Studios",
          publisher: "Electronic Arts",
          release_date: "2021-03-26",
          price: "R$ 79,99",
          image:
            "https://cdn.akamai.steamstatic.com/steam/apps/1426210/header.jpg",
          description:
            "It Takes Two é um jogo de aventura cooperativo para exatamente 2 jogadores.",
          tags: ["Cooperativo", "Aventura", "História", "2 Jogadores"],
          rating: 4.8,
        },
        {
          appid: 1174180,
          name: "A Way Out",
          developer: "Hazelight Studios",
          publisher: "Electronic Arts",
          release_date: "2018-03-23",
          price: "R$ 59,99",
          image:
            "https://cdn.akamai.steamstatic.com/steam/apps/1222700/header.jpg",
          description:
            "A Way Out é um jogo cooperativo de fuga da prisão para 2 jogadores.",
          tags: ["Cooperativo", "Ação", "História", "2 Jogadores"],
          rating: 4.4,
        },
        // Mais jogos indie e metroidvania
        {
          appid: 774361,
          name: "Blasphemous",
          developer: "The Game Kitchen",
          publisher: "Team17",
          release_date: "2019-09-10",
          price: "R$ 49,99",
          image:
            "https://cdn.akamai.steamstatic.com/steam/apps/774361/header.jpg",
          description:
            "Blasphemous é um metroidvania brutal ambientado na terra de Cvstodia.",
          tags: ["Metroidvania", "Indie", "Pixel Art", "Difícil"],
          rating: 4.5,
        },
        {
          appid: 1681120,
          name: "Blasphemous 2",
          developer: "The Game Kitchen",
          publisher: "Team17",
          release_date: "2023-08-24",
          price: "R$ 79,99",
          image:
            "https://cdn.akamai.steamstatic.com/steam/apps/1681120/header.jpg",
          description:
            "Blasphemous 2 é a sequência do aclamado metroidvania pixel art.",
          tags: ["Metroidvania", "Indie", "Pixel Art", "Ação"],
          rating: 4.6,
        },
        // Mais jogos metroidvania similares
        {
          appid: 570940,
          name: "GRIS",
          developer: "Nomada Studio",
          publisher: "Devolver Digital",
          release_date: "2018-12-13",
          price: "R$ 34,99",
          image:
            "https://cdn.akamai.steamstatic.com/steam/apps/570940/header.jpg",
          description:
            "GRIS é uma experiência serena e evocativa, livre de perigo, frustração ou morte.",
          tags: ["Indie", "Aventura", "Artístico", "Relaxante"],
          rating: 4.7,
        },
        {
          appid: 253230,
          name: "A Hat in Time",
          developer: "Gears for Breakfast",
          publisher: "Gears for Breakfast",
          release_date: "2017-10-05",
          price: "R$ 59,99",
          image:
            "https://cdn.akamai.steamstatic.com/steam/apps/253230/header.jpg",
          description:
            "A Hat in Time é um jogo de plataforma 3D cute-as-heck com uma garotinha.",
          tags: ["Plataforma", "3D", "Indie", "Aventura"],
          rating: 4.6,
        },
        {
          appid: 588650,
          name: "Dead Cells",
          developer: "Motion Twin",
          publisher: "Motion Twin",
          release_date: "2018-08-07",
          price: "R$ 49,99",
          image:
            "https://cdn.akamai.steamstatic.com/steam/apps/588650/header.jpg",
          description:
            "Dead Cells é um metroidvania roguelike de ação inspirado em souls-like.",
          tags: ["Metroidvania", "Roguelike", "Indie", "Difícil"],
          rating: 4.8,
        },
        {
          appid: 311690,
          name: "Enter the Gungeon",
          developer: "Dodge Roll",
          publisher: "Devolver Digital",
          release_date: "2016-04-05",
          price: "R$ 29,99",
          image:
            "https://cdn.akamai.steamstatic.com/steam/apps/311690/header.jpg",
          description:
            "Enter the Gungeon é um bullet hell dungeon crawler que segue uma banda de arrependidos.",
          tags: ["Roguelike", "Bullet Hell", "Indie", "Cooperativo"],
          rating: 4.5,
        },
      ];

      // Filtrar resultados baseado na busca
      const filtered = mockResults.filter(
        (game) =>
          game.name.toLowerCase().includes(query.toLowerCase()) ||
          game.developer.toLowerCase().includes(query.toLowerCase()) ||
          game.tags.some((tag) =>
            tag.toLowerCase().includes(query.toLowerCase())
          )
      );

      setSearchResults(filtered);
    } catch (error) {
      console.error("Erro ao buscar jogos:", error);
      setSearchResults([]);
    } finally {
      setLoading(false);
    }
  };

  // Buscar automaticamente quando o usuário digita
  useEffect(() => {
    const delayedSearch = setTimeout(() => {
      if (searchTerm && activeTab === "steam") {
        searchSteamGames(searchTerm);
      } else {
        setSearchResults([]);
      }
    }, 500);

    return () => clearTimeout(delayedSearch);
  }, [searchTerm, activeTab]);

  // Função para adicionar jogo da Steam
  const addSteamGame = async (game) => {
    const gameData = {
      name: game.name,
      developer: game.developer,
      publisher: game.publisher,
      release_date: game.release_date,
      image_url: game.image,
      description: game.description,
      tags: game.tags,
      rating: game.rating,
      steam_appid: game.appid,
      price: game.price,
      source: "steam",
    };

    try {
      const result = await addGameToUser(gameData);
      if (result.success) {
        setSelectedGame(game);
        setTimeout(() => setSelectedGame(null), 3000);
      }
    } catch (error) {
      console.error("Erro ao adicionar jogo:", error);
    }
  };

  // Função para adicionar jogo manual
  const addManualGame = async (e) => {
    e.preventDefault();

    if (!manualGame.name || !manualGame.developer) {
      alert("Por favor, preencha pelo menos o nome e desenvolvedor do jogo");
      return;
    }

    const gameData = {
      name: manualGame.name,
      developer: manualGame.developer,
      publisher: manualGame.publisher || manualGame.developer,
      release_date: manualGame.releaseDate,
      image_url:
        manualGame.imageUrl ||
        "https://via.placeholder.com/460x215?text=No+Image",
      description: manualGame.description,
      genre: manualGame.genre,
      source: "manual",
    };

    try {
      const result = await addGameToUser(gameData);
      if (result.success) {
        // Limpar formulário
        setManualGame({
          name: "",
          developer: "",
          publisher: "",
          genre: "",
          releaseDate: "",
          description: "",
          imageUrl: "",
        });

        alert("Jogo adicionado com sucesso!");
      } else {
        alert("Erro ao adicionar jogo");
      }
    } catch (error) {
      console.error("Erro ao adicionar jogo:", error);
      alert("Erro ao adicionar jogo");
    }
  };

  if (!user) {
    return (
      <div>
        <h1>Acesso Negado</h1>
        <p>Você precisa estar logado para adicionar jogos.</p>
      </div>
    );
  }

  return (
    <div className="main-content">
      <h1>Adicionar Jogos</h1>

      {/* Tabs */}
      <div style={{ marginBottom: "20px" }}>
        <button
          onClick={() => setActiveTab("steam")}
          style={{
            padding: "10px 20px",
            marginRight: "10px",
            backgroundColor: activeTab === "steam" ? "#007bff" : "#f8f9fa",
            color: activeTab === "steam" ? "white" : "black",
            border: "1px solid #ccc",
            cursor: "pointer",
          }}
        >
          Buscar na Steam
        </button>
        <button
          onClick={() => setActiveTab("manual")}
          style={{
            padding: "10px 20px",
            backgroundColor: activeTab === "manual" ? "#007bff" : "#f8f9fa",
            color: activeTab === "manual" ? "white" : "black",
            border: "1px solid #ccc",
            cursor: "pointer",
          }}
        >
          Adicionar Manualmente
        </button>
      </div>

      {/* Conteúdo das Tabs */}
      {activeTab === "steam" && (
        <div>
          <h2>Buscar Jogos na Steam</h2>

          {/* Barra de Pesquisa */}
          <div style={{ marginBottom: "20px" }}>
            <input
              type="text"
              placeholder="Buscar jogos na Steam..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              style={{
                width: "100%",
                padding: "10px",
                fontSize: "16px",
                border: "1px solid #ccc",
                borderRadius: "4px",
              }}
            />
          </div>

          {/* Loading */}
          {loading && <p>Buscando jogos...</p>}

          {/* Mensagem de Sucesso */}
          {selectedGame && (
            <div
              style={{
                backgroundColor: "#d4edda",
                color: "#155724",
                padding: "10px",
                marginBottom: "20px",
                border: "1px solid #c3e6cb",
                borderRadius: "4px",
              }}
            >
              ✅ {selectedGame.name} foi adicionado à sua biblioteca!
            </div>
          )}

          {/* Resultados da Busca */}
          {searchResults.length > 0 && (
            <div>
              <h3>Resultados da Busca ({searchResults.length})</h3>
              <div style={{ display: "grid", gap: "20px" }}>
                {searchResults.map((game) => (
                  <div
                    key={game.appid}
                    style={{
                      border: "1px solid #ccc",
                      borderRadius: "8px",
                      padding: "15px",
                      display: "flex",
                      gap: "15px",
                    }}
                  >
                    <img
                      src={game.image}
                      alt={game.name}
                      style={{
                        width: "184px",
                        height: "69px",
                        objectFit: "cover",
                      }}
                    />
                    <div style={{ flex: 1 }}>
                      <h4>{game.name}</h4>
                      <p>
                        <strong>Desenvolvedor:</strong> {game.developer}
                      </p>
                      <p>
                        <strong>Preço:</strong> {game.price}
                      </p>
                      <p>
                        <strong>Avaliação:</strong> ⭐ {game.rating}/5
                      </p>
                      <p>{game.description}</p>
                      <div style={{ marginTop: "10px" }}>
                        {game.tags.map((tag, index) => (
                          <span
                            key={index}
                            style={{
                              backgroundColor: "#e9ecef",
                              padding: "2px 8px",
                              marginRight: "5px",
                              borderRadius: "12px",
                              fontSize: "12px",
                            }}
                          >
                            {tag}
                          </span>
                        ))}
                      </div>
                      <button
                        onClick={() => addSteamGame(game)}
                        style={{
                          marginTop: "10px",
                          padding: "8px 16px",
                          backgroundColor: "#28a745",
                          color: "white",
                          border: "none",
                          borderRadius: "4px",
                          cursor: "pointer",
                        }}
                      >
                        Adicionar à Biblioteca
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Estado Inicial */}
          {!searchTerm && (
            <div
              style={{ textAlign: "center", padding: "40px", color: "#666" }}
            >
              <p>Digite o nome de um jogo para começar a busca</p>
            </div>
          )}

          {/* Nenhum resultado */}
          {!loading && searchTerm && searchResults.length === 0 && (
            <div
              style={{ textAlign: "center", padding: "40px", color: "#666" }}
            >
              <p>Nenhum jogo encontrado para "{searchTerm}"</p>
            </div>
          )}
        </div>
      )}

      {activeTab === "manual" && (
        <div>
          <h2>Adicionar Jogo Manualmente</h2>

          <form onSubmit={addManualGame} style={{ maxWidth: "600px" }}>
            <div style={{ marginBottom: "15px" }}>
              <label>Nome do Jogo *</label>
              <input
                type="text"
                value={manualGame.name}
                onChange={(e) =>
                  setManualGame({ ...manualGame, name: e.target.value })
                }
                style={{
                  width: "100%",
                  padding: "8px",
                  border: "1px solid #ccc",
                  borderRadius: "4px",
                }}
                required
              />
            </div>

            <div style={{ marginBottom: "15px" }}>
              <label>Desenvolvedor *</label>
              <input
                type="text"
                value={manualGame.developer}
                onChange={(e) =>
                  setManualGame({ ...manualGame, developer: e.target.value })
                }
                style={{
                  width: "100%",
                  padding: "8px",
                  border: "1px solid #ccc",
                  borderRadius: "4px",
                }}
                required
              />
            </div>

            <div style={{ marginBottom: "15px" }}>
              <label>Publisher</label>
              <input
                type="text"
                value={manualGame.publisher}
                onChange={(e) =>
                  setManualGame({ ...manualGame, publisher: e.target.value })
                }
                style={{
                  width: "100%",
                  padding: "8px",
                  border: "1px solid #ccc",
                  borderRadius: "4px",
                }}
              />
            </div>

            <div style={{ marginBottom: "15px" }}>
              <label>Gênero</label>
              <input
                type="text"
                value={manualGame.genre}
                onChange={(e) =>
                  setManualGame({ ...manualGame, genre: e.target.value })
                }
                style={{
                  width: "100%",
                  padding: "8px",
                  border: "1px solid #ccc",
                  borderRadius: "4px",
                }}
                placeholder="Ex: RPG, Ação, Aventura"
              />
            </div>

            <div style={{ marginBottom: "15px" }}>
              <label>Data de Lançamento</label>
              <input
                type="date"
                value={manualGame.releaseDate}
                onChange={(e) =>
                  setManualGame({ ...manualGame, releaseDate: e.target.value })
                }
                style={{
                  width: "100%",
                  padding: "8px",
                  border: "1px solid #ccc",
                  borderRadius: "4px",
                }}
              />
            </div>

            <div style={{ marginBottom: "15px" }}>
              <label>URL da Imagem</label>
              <input
                type="url"
                value={manualGame.imageUrl}
                onChange={(e) =>
                  setManualGame({ ...manualGame, imageUrl: e.target.value })
                }
                style={{
                  width: "100%",
                  padding: "8px",
                  border: "1px solid #ccc",
                  borderRadius: "4px",
                }}
                placeholder="https://exemplo.com/imagem.jpg"
              />
            </div>

            <div style={{ marginBottom: "15px" }}>
              <label>Descrição</label>
              <textarea
                value={manualGame.description}
                onChange={(e) =>
                  setManualGame({ ...manualGame, description: e.target.value })
                }
                style={{
                  width: "100%",
                  padding: "8px",
                  border: "1px solid #ccc",
                  borderRadius: "4px",
                  minHeight: "100px",
                }}
                placeholder="Descrição do jogo..."
              />
            </div>

            <button
              type="submit"
              style={{
                padding: "12px 24px",
                backgroundColor: "#007bff",
                color: "white",
                border: "none",
                borderRadius: "4px",
                cursor: "pointer",
                fontSize: "16px",
              }}
            >
              Adicionar Jogo
            </button>
          </form>
        </div>
      )}
    </div>
  );
};

export default AddGames;
