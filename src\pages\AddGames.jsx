import React, { useState, useEffect } from "react";
import { useAuth } from "../context/AuthContext";

const AddGames = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState("steam");
  const [searchTerm, setSearchTerm] = useState("");
  const [searchResults, setSearchResults] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedGame, setSelectedGame] = useState(null);

  // Estados para adição manual
  const [manualGame, setManualGame] = useState({
    name: "",
    developer: "",
    publisher: "",
    genre: "",
    releaseDate: "",
    description: "",
    imageUrl: "",
  });

  // Função para buscar jogos na Steam (simulada)
  const searchSteamGames = async (query) => {
    if (!query.trim()) return;

    setLoading(true);
    try {
      // Simulação de dados da Steam API
      const mockResults = [
        {
          appid: 1091500,
          name: "Cyberpunk 2077",
          developer: "CD PROJEKT RED",
          publisher: "CD PROJEKT RED",
          release_date: "2020-12-10",
          price: "R$ 199,90",
          image:
            "https://cdn.akamai.steamstatic.com/steam/apps/1091500/header.jpg",
          description:
            "Cyberpunk 2077 é um RPG de ação e aventura em mundo aberto que se passa em Night City.",
          tags: ["RPG", "Mundo Aberto", "Cyberpunk", "Ação"],
          rating: 4.2,
        },
        {
          appid: 292030,
          name: "The Witcher 3: Wild Hunt",
          developer: "CD PROJEKT RED",
          publisher: "CD PROJEKT RED",
          release_date: "2015-05-19",
          price: "R$ 39,99",
          image:
            "https://cdn.akamai.steamstatic.com/steam/apps/292030/header.jpg",
          description:
            "The Witcher 3: Wild Hunt é um RPG de mundo aberto de nova geração.",
          tags: ["RPG", "Mundo Aberto", "Fantasia", "História Rica"],
          rating: 4.8,
        },
        {
          appid: 1174180,
          name: "Red Dead Redemption 2",
          developer: "Rockstar Games",
          publisher: "Rockstar Games",
          release_date: "2019-12-05",
          price: "R$ 299,90",
          image:
            "https://cdn.akamai.steamstatic.com/steam/apps/1174180/header.jpg",
          description:
            "Red Dead Redemption 2 é um épico de ação e aventura ambientado no coração da América.",
          tags: ["Ação", "Aventura", "Mundo Aberto", "Western"],
          rating: 4.6,
        },
        {
          appid: 271590,
          name: "Grand Theft Auto V",
          developer: "Rockstar North",
          publisher: "Rockstar Games",
          release_date: "2015-04-14",
          price: "R$ 89,90",
          image:
            "https://cdn.akamai.steamstatic.com/steam/apps/271590/header.jpg",
          description:
            "Grand Theft Auto V para PC oferece aos jogadores a opção de explorar o mundo premiado e enorme de Los Santos.",
          tags: ["Ação", "Aventura", "Mundo Aberto", "Crime"],
          rating: 4.4,
        },
      ];

      // Filtrar resultados baseado na busca
      const filtered = mockResults.filter(
        (game) =>
          game.name.toLowerCase().includes(query.toLowerCase()) ||
          game.developer.toLowerCase().includes(query.toLowerCase()) ||
          game.tags.some((tag) =>
            tag.toLowerCase().includes(query.toLowerCase())
          )
      );

      setSearchResults(filtered);
    } catch (error) {
      console.error("Erro ao buscar jogos:", error);
      setSearchResults([]);
    } finally {
      setLoading(false);
    }
  };

  // Buscar automaticamente quando o usuário digita
  useEffect(() => {
    const delayedSearch = setTimeout(() => {
      if (searchTerm && activeTab === "steam") {
        searchSteamGames(searchTerm);
      } else {
        setSearchResults([]);
      }
    }, 500);

    return () => clearTimeout(delayedSearch);
  }, [searchTerm, activeTab]);

  // Função para adicionar jogo da Steam
  const addSteamGame = (game) => {
    const gameData = {
      id: Date.now(),
      name: game.name,
      developer: game.developer,
      publisher: game.publisher,
      release_date: game.release_date,
      image_url: game.image,
      description: game.description,
      tags: game.tags,
      rating: game.rating,
      steam_appid: game.appid,
      price: game.price,
      added_by: user.username,
      added_date: new Date().toISOString(),
      source: "steam",
    };

    // Salvar no banco de dados local
    const games = JSON.parse(localStorage.getItem("gamesDatabase") || "[]");
    games.push(gameData);
    localStorage.setItem("gamesDatabase", JSON.stringify(games));

    setSelectedGame(game);
    setTimeout(() => setSelectedGame(null), 3000);
  };

  // Função para adicionar jogo manual
  const addManualGame = (e) => {
    e.preventDefault();

    if (!manualGame.name || !manualGame.developer) {
      alert("Por favor, preencha pelo menos o nome e desenvolvedor do jogo");
      return;
    }

    const gameData = {
      id: Date.now(),
      name: manualGame.name,
      developer: manualGame.developer,
      publisher: manualGame.publisher || manualGame.developer,
      release_date: manualGame.releaseDate,
      image_url:
        manualGame.imageUrl ||
        "https://via.placeholder.com/460x215?text=No+Image",
      description: manualGame.description,
      genre: manualGame.genre,
      added_by: user.username,
      added_date: new Date().toISOString(),
      source: "manual",
    };

    // Salvar no banco de dados local
    const games = JSON.parse(localStorage.getItem("gamesDatabase") || "[]");
    games.push(gameData);
    localStorage.setItem("gamesDatabase", JSON.stringify(games));

    // Limpar formulário
    setManualGame({
      name: "",
      developer: "",
      publisher: "",
      genre: "",
      releaseDate: "",
      description: "",
      imageUrl: "",
    });

    alert("Jogo adicionado com sucesso!");
  };

  if (!user) {
    return (
      <div>
        <h1>Acesso Negado</h1>
        <p>Você precisa estar logado para adicionar jogos.</p>
      </div>
    );
  }

  return (
    <div className="main-content">
      <h1>Adicionar Jogos</h1>

      {/* Tabs */}
      <div style={{ marginBottom: "20px" }}>
        <button
          onClick={() => setActiveTab("steam")}
          style={{
            padding: "10px 20px",
            marginRight: "10px",
            backgroundColor: activeTab === "steam" ? "#007bff" : "#f8f9fa",
            color: activeTab === "steam" ? "white" : "black",
            border: "1px solid #ccc",
            cursor: "pointer",
          }}
        >
          Buscar na Steam
        </button>
        <button
          onClick={() => setActiveTab("manual")}
          style={{
            padding: "10px 20px",
            backgroundColor: activeTab === "manual" ? "#007bff" : "#f8f9fa",
            color: activeTab === "manual" ? "white" : "black",
            border: "1px solid #ccc",
            cursor: "pointer",
          }}
        >
          Adicionar Manualmente
        </button>
      </div>

      {/* Conteúdo das Tabs */}
      {activeTab === "steam" && (
        <div>
          <h2>Buscar Jogos na Steam</h2>

          {/* Barra de Pesquisa */}
          <div style={{ marginBottom: "20px" }}>
            <input
              type="text"
              placeholder="Buscar jogos na Steam..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              style={{
                width: "100%",
                padding: "10px",
                fontSize: "16px",
                border: "1px solid #ccc",
                borderRadius: "4px",
              }}
            />
          </div>

          {/* Loading */}
          {loading && <p>Buscando jogos...</p>}

          {/* Mensagem de Sucesso */}
          {selectedGame && (
            <div
              style={{
                backgroundColor: "#d4edda",
                color: "#155724",
                padding: "10px",
                marginBottom: "20px",
                border: "1px solid #c3e6cb",
                borderRadius: "4px",
              }}
            >
              ✅ {selectedGame.name} foi adicionado à sua biblioteca!
            </div>
          )}

          {/* Resultados da Busca */}
          {searchResults.length > 0 && (
            <div>
              <h3>Resultados da Busca ({searchResults.length})</h3>
              <div style={{ display: "grid", gap: "20px" }}>
                {searchResults.map((game) => (
                  <div
                    key={game.appid}
                    style={{
                      border: "1px solid #ccc",
                      borderRadius: "8px",
                      padding: "15px",
                      display: "flex",
                      gap: "15px",
                    }}
                  >
                    <img
                      src={game.image}
                      alt={game.name}
                      style={{
                        width: "184px",
                        height: "69px",
                        objectFit: "cover",
                      }}
                    />
                    <div style={{ flex: 1 }}>
                      <h4>{game.name}</h4>
                      <p>
                        <strong>Desenvolvedor:</strong> {game.developer}
                      </p>
                      <p>
                        <strong>Preço:</strong> {game.price}
                      </p>
                      <p>
                        <strong>Avaliação:</strong> ⭐ {game.rating}/5
                      </p>
                      <p>{game.description}</p>
                      <div style={{ marginTop: "10px" }}>
                        {game.tags.map((tag, index) => (
                          <span
                            key={index}
                            style={{
                              backgroundColor: "#e9ecef",
                              padding: "2px 8px",
                              marginRight: "5px",
                              borderRadius: "12px",
                              fontSize: "12px",
                            }}
                          >
                            {tag}
                          </span>
                        ))}
                      </div>
                      <button
                        onClick={() => addSteamGame(game)}
                        style={{
                          marginTop: "10px",
                          padding: "8px 16px",
                          backgroundColor: "#28a745",
                          color: "white",
                          border: "none",
                          borderRadius: "4px",
                          cursor: "pointer",
                        }}
                      >
                        Adicionar à Biblioteca
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Estado Inicial */}
          {!searchTerm && (
            <div
              style={{ textAlign: "center", padding: "40px", color: "#666" }}
            >
              <p>Digite o nome de um jogo para começar a busca</p>
            </div>
          )}

          {/* Nenhum resultado */}
          {!loading && searchTerm && searchResults.length === 0 && (
            <div
              style={{ textAlign: "center", padding: "40px", color: "#666" }}
            >
              <p>Nenhum jogo encontrado para "{searchTerm}"</p>
            </div>
          )}
        </div>
      )}

      {activeTab === "manual" && (
        <div>
          <h2>Adicionar Jogo Manualmente</h2>

          <form onSubmit={addManualGame} style={{ maxWidth: "600px" }}>
            <div style={{ marginBottom: "15px" }}>
              <label>Nome do Jogo *</label>
              <input
                type="text"
                value={manualGame.name}
                onChange={(e) =>
                  setManualGame({ ...manualGame, name: e.target.value })
                }
                style={{
                  width: "100%",
                  padding: "8px",
                  border: "1px solid #ccc",
                  borderRadius: "4px",
                }}
                required
              />
            </div>

            <div style={{ marginBottom: "15px" }}>
              <label>Desenvolvedor *</label>
              <input
                type="text"
                value={manualGame.developer}
                onChange={(e) =>
                  setManualGame({ ...manualGame, developer: e.target.value })
                }
                style={{
                  width: "100%",
                  padding: "8px",
                  border: "1px solid #ccc",
                  borderRadius: "4px",
                }}
                required
              />
            </div>

            <div style={{ marginBottom: "15px" }}>
              <label>Publisher</label>
              <input
                type="text"
                value={manualGame.publisher}
                onChange={(e) =>
                  setManualGame({ ...manualGame, publisher: e.target.value })
                }
                style={{
                  width: "100%",
                  padding: "8px",
                  border: "1px solid #ccc",
                  borderRadius: "4px",
                }}
              />
            </div>

            <div style={{ marginBottom: "15px" }}>
              <label>Gênero</label>
              <input
                type="text"
                value={manualGame.genre}
                onChange={(e) =>
                  setManualGame({ ...manualGame, genre: e.target.value })
                }
                style={{
                  width: "100%",
                  padding: "8px",
                  border: "1px solid #ccc",
                  borderRadius: "4px",
                }}
                placeholder="Ex: RPG, Ação, Aventura"
              />
            </div>

            <div style={{ marginBottom: "15px" }}>
              <label>Data de Lançamento</label>
              <input
                type="date"
                value={manualGame.releaseDate}
                onChange={(e) =>
                  setManualGame({ ...manualGame, releaseDate: e.target.value })
                }
                style={{
                  width: "100%",
                  padding: "8px",
                  border: "1px solid #ccc",
                  borderRadius: "4px",
                }}
              />
            </div>

            <div style={{ marginBottom: "15px" }}>
              <label>URL da Imagem</label>
              <input
                type="url"
                value={manualGame.imageUrl}
                onChange={(e) =>
                  setManualGame({ ...manualGame, imageUrl: e.target.value })
                }
                style={{
                  width: "100%",
                  padding: "8px",
                  border: "1px solid #ccc",
                  borderRadius: "4px",
                }}
                placeholder="https://exemplo.com/imagem.jpg"
              />
            </div>

            <div style={{ marginBottom: "15px" }}>
              <label>Descrição</label>
              <textarea
                value={manualGame.description}
                onChange={(e) =>
                  setManualGame({ ...manualGame, description: e.target.value })
                }
                style={{
                  width: "100%",
                  padding: "8px",
                  border: "1px solid #ccc",
                  borderRadius: "4px",
                  minHeight: "100px",
                }}
                placeholder="Descrição do jogo..."
              />
            </div>

            <button
              type="submit"
              style={{
                padding: "12px 24px",
                backgroundColor: "#007bff",
                color: "white",
                border: "none",
                borderRadius: "4px",
                cursor: "pointer",
                fontSize: "16px",
              }}
            >
              Adicionar Jogo
            </button>
          </form>
        </div>
      )}
    </div>
  );
};

export default AddGames;
