import React, { useState } from "react";
import { motion } from "framer-motion";
import {
  Users,
  Clock,
  Library,
  Trophy,
  Monitor,
  Play,
  Building,
  Plus,
} from "lucide-react";
import { useGames } from "../context/GameContext";
import { useAuth } from "../context/AuthContext";
import GameModal from "../components/GameModal";

const Home = () => {
  const { games, loading, getTopPlayers, getStats, pcConfig } = useGames();
  const { user } = useAuth();
  const [selectedGame, setSelectedGame] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-white"></div>
      </div>
    );
  }

  const stats = getStats();
  const topPlayers = getTopPlayers();

  const handleLogPlay = (game) => {
    setSelectedGame(game);
    setIsModalOpen(true);
  };

  const getRankEmoji = (position) => {
    switch (position) {
      case 1:
        return "💎";
      case 2:
        return "🥇";
      case 3:
        return "🥈";
      case 4:
        return "🥉";
      default:
        return "💩";
    }
  };

  return (
    <div className="min-h-screen p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <h1 className="text-5xl font-bold gradient-text mb-4">
            Game Tracker Dashboard
          </h1>
          <p className="text-xl text-gray-300">
            Track your gaming journey and compete with friends
          </p>
        </motion.div>

        {/* Dashboard Statistics */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="glass-card mb-8"
        >
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="stat-card">
              <div className="flex items-center justify-center w-16 h-16 mx-auto mb-4 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full glow-animation">
                <Users className="h-8 w-8 text-white" />
              </div>
              <div className="text-3xl font-bold text-white mb-2 text-glow">
                {stats.total_players}
              </div>
              <div className="text-sm text-gray-300 uppercase tracking-wider">
                Active Players
              </div>
            </div>

            <div className="stat-card">
              <div className="flex items-center justify-center w-16 h-16 mx-auto mb-4 bg-gradient-to-r from-green-500 to-emerald-600 rounded-full glow-animation">
                <Clock className="h-8 w-8 text-white" />
              </div>
              <div className="text-3xl font-bold text-white mb-2 text-glow">
                {Math.round(stats.total_hours)}
              </div>
              <div className="text-sm text-gray-300 uppercase tracking-wider">
                Total Hours
              </div>
            </div>

            <div className="stat-card">
              <div className="flex items-center justify-center w-16 h-16 mx-auto mb-4 bg-gradient-to-r from-yellow-500 to-orange-600 rounded-full glow-animation">
                <Library className="h-8 w-8 text-white" />
              </div>
              <div className="text-3xl font-bold text-white mb-2 text-glow">
                {stats.total_games}
              </div>
              <div className="text-sm text-gray-300 uppercase tracking-wider">
                Games Library
              </div>
            </div>

            <div className="stat-card">
              <div className="flex items-center justify-center w-16 h-16 mx-auto mb-4 bg-gradient-to-r from-pink-500 to-red-600 rounded-full glow-animation">
                <Trophy className="h-8 w-8 text-white" />
              </div>
              <div className="text-3xl font-bold text-white mb-2 text-glow">
                {stats.completed_gameplays}
              </div>
              <div className="text-sm text-gray-300 uppercase tracking-wider">
                Completed Sessions
              </div>
            </div>
          </div>
        </motion.div>

        {/* Config PC Rank and Top Players Row */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* CONFIG PC RANK */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="glass-card"
          >
            <div className="flex items-center mb-6">
              <Monitor className="h-6 w-6 text-blue-400 mr-3" />
              <h3 className="text-xl font-semibold text-white">
                CONFIG PC RANK
              </h3>
            </div>
            <div className="text-center py-8">
              <div className="text-6xl mb-4">{pcConfig.rank.emoji}</div>
              <div className="text-xl font-bold text-white mb-2">
                {pcConfig.rank.name}
              </div>
              <div className="text-gray-400 mb-4">
                {pcConfig.cpu ? 'Configuração ativa' : 'Configure seu PC no Profile'}
              </div>
              {pcConfig.cpu && (
                <div className="space-y-2 text-sm text-gray-300">
                  <div className="flex justify-between">
                    <span>CPU:</span>
                    <span className="text-blue-400">{pcConfig.cpu.replace('-', ' ').toUpperCase()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>GPU:</span>
                    <span className="text-green-400">{pcConfig.gpu.replace('-', ' ').toUpperCase()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>RAM:</span>
                    <span className="text-purple-400">{pcConfig.ram.toUpperCase()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Storage:</span>
                    <span className="text-yellow-400">{pcConfig.storage.replace('-', ' ').toUpperCase()}</span>
                  </div>
                </div>
              )}</div>
            </div>
          </motion.div>

          {/* TOP PLAYERS */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="glass-card"
          >
            <div className="flex items-center mb-6">
              <Trophy className="h-6 w-6 text-yellow-400 mr-3" />
              <h3 className="text-xl font-semibold text-white">Top Players</h3>
            </div>
            <div className="space-y-4">
              {topPlayers.slice(0, 5).map((player, index) => (
                <div
                  key={player.id}
                  className="flex items-center p-3 rounded-lg bg-white/5 hover:bg-white/10 transition-all duration-200"
                >
                  <div className="flex items-center justify-center w-10 h-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 text-white font-bold text-lg mr-4">
                    {getRankEmoji(index + 1)}
                  </div>
                  <div className="flex-grow">
                    <div className="font-medium text-white">
                      {player.username}
                    </div>
                    <div className="text-sm text-gray-400">
                      {player.completed_games} games completed
                    </div>
                  </div>
                  <div className="text-blue-400 font-semibold">
                    {player.total_hours.toFixed(1)}h
                  </div>
                </div>
              ))}
            </div>
          </motion.div>
        </div>

        {/* Games Library Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-3xl font-bold text-white">Games Library</h2>
            <button className="btn-primary flex items-center space-x-2">
              <Plus className="h-5 w-5" />
              <span>Add Game</span>
            </button>
          </div>

          {games.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {games.map((game, index) => (
                <motion.div
                  key={game.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.1 * index }}
                  className="game-card group"
                >
                  <div className="relative overflow-hidden rounded-lg mb-4">
                    <img
                      src={game.image_url}
                      alt={game.name}
                      className="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-300"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
                  </div>

                  <h3 className="text-lg font-semibold text-white mb-3">
                    {game.name}
                  </h3>

                  {game.publisher && (
                    <div className="flex items-center mb-3">
                      <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-500/20 text-blue-400 border border-blue-500/30">
                        <Building className="h-3 w-3 mr-1" />
                        {game.publisher}
                      </span>
                    </div>
                  )}

                  <div className="mb-4">
                    <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gradient-to-r from-pink-500 to-red-600 text-white">
                      {game.price}
                    </span>
                  </div>

                  <button
                    onClick={() => handleLogPlay(game)}
                    className="w-full btn-success flex items-center justify-center space-x-2"
                  >
                    <Play className="h-4 w-4" />
                    <span>Log Play</span>
                  </button>
                </motion.div>
              ))}
            </div>
          ) : (
            <div className="text-center py-16">
              <Library className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-white mb-2">
                No games in your library yet!
              </h3>
              <p className="text-gray-400 mb-6">
                Start building your collection by adding games.
              </p>
              <div className="space-x-4">
                <button className="btn-primary">Add Game from Steam</button>
                <button className="btn-secondary">Add Game Manually</button>
              </div>
            </div>
          )}
        </motion.div>
      </div>

      {/* Game Modal */}
      <GameModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        game={selectedGame}
      />
    </div>
  );
};

export default Home;
