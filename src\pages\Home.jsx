import React from "react";
import { Link } from "react-router-dom";
import { useAuth } from "../context/AuthContext";

const Home = () => {
  const { user } = useAuth();

  return (
    <div>
      <h1>Bem-vindo ao Sistema</h1>

      {user ? (
        <div>
          <p>
            <PERSON><PERSON><PERSON>, {user.name}! (@{user.username})
          </p>
          <nav>
            <Link to="/profile">Meu Perfil</Link> |{" "}
            <Link to="/add-games">Adicionar <PERSON></Link> |{" "}
            <Link to="/library">Biblioteca</Link> |{" "}
            <Link to="/database">Ver Banco de Dados</Link>
          </nav>
        </div>
      ) : (
        <div>
          <p>Faça login para acessar o sistema</p>
          <nav>
            <Link to="/login">Login</Link> |{" "}
            <Link to="/register">Registrar</Link> |{" "}
            <Link to="/database">Ver Banco de Dados</Link>
          </nav>
        </div>
      )}
    </div>
  );
};

export default Home;
