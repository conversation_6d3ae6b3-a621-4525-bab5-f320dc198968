import React, { useState, useEffect } from "react";
import { Link, Navigate } from "react-router-dom";
import { useAuth } from "../context/AuthContext";

const Home = () => {
  const { user, getLogs, getTopPlayers, getTopPc } = useAuth();
  const [logs, setLogs] = useState([]);
  const [topPlayers, setTopPlayers] = useState([]);
  const [topPc, setTopPc] = useState([]);

  useEffect(() => {
    const loadData = async () => {
      try {
        setLogs(getLogs().slice(0, 10)); // Logs ainda são síncronos
        const players = await getTopPlayers();
        setTopPlayers(players);
        setTopPc(getTopPc());
      } catch (error) {
        console.error("Erro ao carregar dados:", error);
      }
    };

    loadData();
  }, [getLogs, getTopPlayers, getTopPc]);

  // Redirecionar usuários não logados para a página de boas-vindas
  if (!user) {
    return <Navigate to="/welcome" replace />;
  }

  return (
    <div className="main-content">
      <h1>Bem-vindo ao GameTracker</h1>
      <div>
        <p>
          Olá, {user.name}! (@{user.username})
        </p>
        {user.pcRank && (
          <div
            style={{
              backgroundColor: "#f8f9fa",
              border: "2px solid #dee2e6",
              borderRadius: "8px",
              padding: "1rem",
              marginBottom: "1rem",
              textAlign: "center",
              maxWidth: "300px",
            }}
          >
            <h3 style={{ margin: "0 0 0.5rem 0", color: "#495057" }}>
              PC Rank
            </h3>
            <div style={{ fontSize: "2rem", marginBottom: "0.5rem" }}>
              {user.pcRank.emoji}
            </div>
            <div
              style={{
                fontSize: "1.2rem",
                fontWeight: "bold",
                color: "#495057",
              }}
            >
              {user.pcRank.rank}
            </div>
            <div style={{ fontSize: "0.9rem", color: "#6c757d" }}>
              {user.pcRank.points} pontos
            </div>
          </div>
        )}
        <p>
          Bem-vindo de volta! Use a navegação acima para acessar suas
          funcionalidades.
        </p>

        <div style={{ marginTop: "2rem" }}>
          <h2>Ações Rápidas</h2>
          <div
            style={{
              display: "flex",
              gap: "1rem",
              flexWrap: "wrap",
              marginTop: "1rem",
            }}
          >
            <Link to="/add-games" className="btn btn-primary">
              🎮 Adicionar Jogos
            </Link>
            <Link to="/library" className="btn btn-secondary">
              📚 Ver Biblioteca
            </Link>
            <Link to="/profile" className="btn btn-secondary">
              👤 Meu Perfil
            </Link>
          </div>
        </div>
      </div>
      {/* Seção de Logs - Apenas para Admin */}
      {user.isAdmin && (
        <div style={{ marginTop: "3rem" }}>
          <h2>🔍 Logs do Sistema (Admin)</h2>
          {logs.length > 0 ? (
            <div
              style={{
                backgroundColor: "#f8f9fa",
                border: "1px solid #dee2e6",
                borderRadius: "8px",
                padding: "1rem",
                marginTop: "1rem",
                maxHeight: "400px",
                overflowY: "auto",
              }}
            >
              {logs.map((log) => (
                <div
                  key={log.id}
                  style={{
                    padding: "0.5rem 0",
                    borderBottom: "1px solid #e9ecef",
                    fontSize: "0.9rem",
                  }}
                >
                  <div
                    style={{
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "center",
                    }}
                  >
                    <span
                      style={{
                        fontWeight: "bold",
                        color: getLogColor(log.action),
                      }}
                    >
                      {getLogIcon(log.action)} {log.action.replace("_", " ")}
                    </span>
                    <span style={{ color: "#6c757d", fontSize: "0.8rem" }}>
                      {new Date(log.timestamp).toLocaleString("pt-BR")}
                    </span>
                  </div>
                  <div style={{ color: "#495057", marginTop: "0.25rem" }}>
                    <strong>{log.user}:</strong> {log.details}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p
              style={{
                color: "#6c757d",
                fontStyle: "italic",
                marginTop: "1rem",
              }}
            >
              Nenhuma atividade registrada ainda.
            </p>
          )}
        </div>
      )}
      {/* Seções de Rankings */}
      <div
        style={{
          display: "grid",
          gridTemplateColumns: "repeat(auto-fit, minmax(300px, 1fr))",
          gap: "2rem",
          marginTop: "3rem",
        }}
      >
        {/* Top Players */}
        <div>
          <h2>🏆 Top Players</h2>
          <div
            style={{
              backgroundColor: "#f8f9fa",
              border: "1px solid #dee2e6",
              borderRadius: "8px",
              padding: "1rem",
              marginTop: "1rem",
            }}
          >
            {topPlayers && topPlayers.length > 0 ? (
              topPlayers.map((player, index) => (
                <div
                  key={player.id}
                  style={{
                    display: "flex",
                    alignItems: "center",
                    padding: "0.75rem 0",
                    borderBottom:
                      index < topPlayers.length - 1
                        ? "1px solid #e9ecef"
                        : "none",
                  }}
                >
                  <div
                    style={{
                      fontSize: "1.5rem",
                      marginRight: "1rem",
                      minWidth: "30px",
                    }}
                  >
                    {index === 0 && "🥇"}
                    {index === 1 && "🥈"}
                    {index === 2 && "🥉"}
                    {index > 2 && `${index + 1}º`}
                  </div>
                  <img
                    src={player.avatar}
                    alt={player.name}
                    style={{
                      width: "40px",
                      height: "40px",
                      borderRadius: "50%",
                      marginRight: "1rem",
                    }}
                  />
                  <div style={{ flex: 1 }}>
                    <div style={{ fontWeight: "bold", color: "#495057" }}>
                      {player.name}
                    </div>
                    <div style={{ fontSize: "0.9rem", color: "#6c757d" }}>
                      @{player.username} • {player.totalGames} jogos total •{" "}
                      {player.completedGames} completados
                    </div>
                  </div>
                  <div
                    style={{
                      backgroundColor: "#007bff",
                      color: "white",
                      padding: "0.25rem 0.5rem",
                      borderRadius: "12px",
                      fontSize: "0.8rem",
                    }}
                  >
                    {player.totalGames} jogos
                  </div>
                </div>
              ))
            ) : (
              <p
                style={{
                  color: "#6c757d",
                  textAlign: "center",
                  margin: "2rem 0",
                }}
              >
                Nenhum usuário cadastrado ainda. Registre-se para aparecer no
                ranking!
              </p>
            )}
          </div>
        </div>

        {/* Top PC */}
        <div>
          <h2>💻 Top PC</h2>
          <div
            style={{
              backgroundColor: "#f8f9fa",
              border: "1px solid #dee2e6",
              borderRadius: "8px",
              padding: "1rem",
              marginTop: "1rem",
            }}
          >
            {topPc.length > 0 ? (
              topPc.map((player, index) => (
                <div
                  key={player.id}
                  style={{
                    display: "flex",
                    alignItems: "center",
                    padding: "0.75rem 0",
                    borderBottom:
                      index < topPc.length - 1 ? "1px solid #e9ecef" : "none",
                  }}
                >
                  <div
                    style={{
                      fontSize: "1.5rem",
                      marginRight: "1rem",
                      minWidth: "30px",
                    }}
                  >
                    {index === 0 && "🥇"}
                    {index === 1 && "🥈"}
                    {index === 2 && "🥉"}
                    {index > 2 && `${index + 1}º`}
                  </div>
                  <img
                    src={player.avatar}
                    alt={player.name}
                    style={{
                      width: "40px",
                      height: "40px",
                      borderRadius: "50%",
                      marginRight: "1rem",
                    }}
                  />
                  <div style={{ flex: 1 }}>
                    <div style={{ fontWeight: "bold", color: "#495057" }}>
                      {player.name}
                    </div>
                    <div style={{ fontSize: "0.9rem", color: "#6c757d" }}>
                      @{player.username}
                    </div>
                    {player.pcSpecs && player.pcSpecs.gpu && (
                      <div style={{ fontSize: "0.8rem", color: "#6c757d" }}>
                        {player.pcSpecs.gpu}
                      </div>
                    )}
                  </div>
                  <div style={{ textAlign: "right" }}>
                    <div style={{ fontSize: "1.5rem" }}>
                      {player.pcRank.emoji}
                    </div>
                    <div
                      style={{
                        fontSize: "0.8rem",
                        color: "#6c757d",
                      }}
                    >
                      {player.pcRank.points} pts
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <p
                style={{
                  color: "#6c757d",
                  textAlign: "center",
                  margin: "2rem 0",
                }}
              >
                Nenhum PC configurado ainda
              </p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

// Função para obter cor do log baseado na ação
const getLogColor = (action) => {
  switch (action) {
    case "ADMIN_LOGIN":
      return "#dc3545";
    case "USER_LOGIN":
      return "#28a745";
    case "USER_REGISTER":
      return "#007bff";
    case "PROFILE_UPDATE":
      return "#ffc107";
    case "GAME_ADDED":
      return "#17a2b8";
    default:
      return "#6c757d";
  }
};

// Função para obter ícone do log baseado na ação
const getLogIcon = (action) => {
  switch (action) {
    case "ADMIN_LOGIN":
      return "🔑";
    case "USER_LOGIN":
      return "👤";
    case "USER_REGISTER":
      return "📝";
    case "PROFILE_UPDATE":
      return "✏️";
    case "GAME_ADDED":
      return "🎮";
    default:
      return "📋";
  }
};

export default Home;
