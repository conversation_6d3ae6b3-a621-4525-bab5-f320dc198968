import React from "react";
import { Link } from "react-router-dom";
import { useAuth } from "../context/AuthContext";

const Home = () => {
  const { user } = useAuth();

  return (
    <div className="main-content">
      <h1>Bem-vindo ao GameTracker</h1>

      {user ? (
        <div>
          <p>
            <PERSON><PERSON><PERSON>, {user.name}! (@{user.username})
          </p>
          <p>
            Bem-vindo de volta! Use a navegação acima para acessar suas
            funcionalidades.
          </p>

          <div style={{ marginTop: "2rem" }}>
            <h2>Ações Rápidas</h2>
            <div
              style={{
                display: "flex",
                gap: "1rem",
                flexWrap: "wrap",
                marginTop: "1rem",
              }}
            >
              <Link to="/add-games" className="btn btn-primary">
                🎮 Adicionar Jogos
              </Link>
              <Link to="/library" className="btn btn-secondary">
                📚 Ver Biblioteca
              </Link>
              <Link to="/profile" className="btn btn-secondary">
                👤 <PERSON>u <PERSON>l
              </Link>
            </div>
          </div>
        </div>
      ) : (
        <div>
          <p>
            Faça login para acessar o sistema completo de rastreamento de jogos.
          </p>

          <div style={{ marginTop: "2rem" }}>
            <h2>Começar Agora</h2>
            <div
              style={{
                display: "flex",
                gap: "1rem",
                flexWrap: "wrap",
                marginTop: "1rem",
              }}
            >
              <Link to="/login" className="btn btn-primary">
                🔑 Fazer Login
              </Link>
              <Link to="/register" className="btn btn-secondary">
                📝 Criar Conta
              </Link>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Home;
