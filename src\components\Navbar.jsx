import React, { useState } from "react";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { useAuth } from "../context/AuthContext";

const Navbar = () => {
  const { user, logout } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const handleLogout = () => {
    logout();
    navigate("/");
    setIsMenuOpen(false);
  };

  const isActive = (path) => {
    return location.pathname === path;
  };

  const navStyle = {
    backgroundColor: "#343a40",
    padding: "1rem 0",
    boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
  };

  const containerStyle = {
    maxWidth: "1200px",
    margin: "0 auto",
    padding: "0 1rem",
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
  };

  const logoStyle = {
    color: "#fff",
    fontSize: "1.5rem",
    fontWeight: "bold",
    textDecoration: "none",
  };

  const menuStyle = {
    display: "flex",
    listStyle: "none",
    margin: 0,
    padding: 0,
    gap: "1rem",
    alignItems: "center",
  };

  const linkStyle = {
    color: "#fff",
    textDecoration: "none",
    padding: "0.5rem 1rem",
    borderRadius: "4px",
    transition: "background-color 0.3s",
  };

  const activeLinkStyle = {
    ...linkStyle,
    backgroundColor: "#007bff",
  };

  const buttonStyle = {
    backgroundColor: "#dc3545",
    color: "#fff",
    border: "none",
    padding: "0.5rem 1rem",
    borderRadius: "4px",
    cursor: "pointer",
    textDecoration: "none",
    fontSize: "14px",
  };

  const mobileMenuStyle = {
    display: "none",
    flexDirection: "column",
    position: "absolute",
    top: "100%",
    left: 0,
    right: 0,
    backgroundColor: "#343a40",
    padding: "1rem",
    boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
  };

  const hamburgerStyle = {
    display: "none",
    flexDirection: "column",
    cursor: "pointer",
    padding: "0.5rem",
  };

  const hamburgerLineStyle = {
    width: "25px",
    height: "3px",
    backgroundColor: "#fff",
    margin: "2px 0",
    transition: "0.3s",
  };

  return (
    <nav style={navStyle}>
      <div style={containerStyle}>
        {/* Logo */}
        <Link to="/" style={logoStyle}>
          🎮 GameTracker
        </Link>

        {/* Desktop Menu */}
        <ul
          style={{
            ...menuStyle,
            "@media (max-width: 768px)": { display: "none" },
          }}
        >
          <li>
            <Link to="/" style={isActive("/") ? activeLinkStyle : linkStyle}>
              Home
            </Link>
          </li>

          {user ? (
            <>
              <li>
                <Link
                  to="/add-games"
                  style={isActive("/add-games") ? activeLinkStyle : linkStyle}
                >
                  Adicionar Jogos
                </Link>
              </li>
              <li>
                <Link
                  to="/library"
                  style={isActive("/library") ? activeLinkStyle : linkStyle}
                >
                  Biblioteca
                </Link>
              </li>
              <li>
                <Link
                  to="/profile"
                  style={isActive("/profile") ? activeLinkStyle : linkStyle}
                >
                  Perfil
                </Link>
              </li>
              <li>
                <Link
                  to="/database"
                  style={isActive("/database") ? activeLinkStyle : linkStyle}
                >
                  Database
                </Link>
              </li>
              <li>
                <span style={{ color: "#fff", marginRight: "1rem" }}>
                  Olá, {user.name}! {user.isAdmin && "👑"}
                </span>
              </li>
              <li>
                <button onClick={handleLogout} style={buttonStyle}>
                  Sair
                </button>
              </li>
            </>
          ) : (
            <>
              <li>
                <Link
                  to="/login"
                  style={isActive("/login") ? activeLinkStyle : linkStyle}
                >
                  Login
                </Link>
              </li>
              <li>
                <Link
                  to="/register"
                  style={isActive("/register") ? activeLinkStyle : linkStyle}
                >
                  Registrar
                </Link>
              </li>
              <li>
                <Link
                  to="/database"
                  style={isActive("/database") ? activeLinkStyle : linkStyle}
                >
                  Database
                </Link>
              </li>
            </>
          )}
        </ul>

        {/* Mobile Hamburger */}
        <div style={hamburgerStyle} onClick={() => setIsMenuOpen(!isMenuOpen)}>
          <div style={hamburgerLineStyle}></div>
          <div style={hamburgerLineStyle}></div>
          <div style={hamburgerLineStyle}></div>
        </div>
      </div>

      {/* Mobile Menu */}
      {isMenuOpen && (
        <div style={mobileMenuStyle}>
          <Link
            to="/"
            style={{ ...linkStyle, display: "block", marginBottom: "0.5rem" }}
            onClick={() => setIsMenuOpen(false)}
          >
            Home
          </Link>

          {user ? (
            <>
              <Link
                to="/add-games"
                style={{
                  ...linkStyle,
                  display: "block",
                  marginBottom: "0.5rem",
                }}
                onClick={() => setIsMenuOpen(false)}
              >
                Adicionar Jogos
              </Link>
              <Link
                to="/library"
                style={{
                  ...linkStyle,
                  display: "block",
                  marginBottom: "0.5rem",
                }}
                onClick={() => setIsMenuOpen(false)}
              >
                Biblioteca
              </Link>
              <Link
                to="/profile"
                style={{
                  ...linkStyle,
                  display: "block",
                  marginBottom: "0.5rem",
                }}
                onClick={() => setIsMenuOpen(false)}
              >
                Perfil
              </Link>
              <Link
                to="/database"
                style={{
                  ...linkStyle,
                  display: "block",
                  marginBottom: "0.5rem",
                }}
                onClick={() => setIsMenuOpen(false)}
              >
                Database
              </Link>
              <div
                style={{
                  color: "#fff",
                  padding: "0.5rem 1rem",
                  marginBottom: "0.5rem",
                }}
              >
                Olá, {user.name}!
              </div>
              <button
                onClick={handleLogout}
                style={{ ...buttonStyle, display: "block", width: "100%" }}
              >
                Sair
              </button>
            </>
          ) : (
            <>
              <Link
                to="/login"
                style={{
                  ...linkStyle,
                  display: "block",
                  marginBottom: "0.5rem",
                }}
                onClick={() => setIsMenuOpen(false)}
              >
                Login
              </Link>
              <Link
                to="/register"
                style={{
                  ...linkStyle,
                  display: "block",
                  marginBottom: "0.5rem",
                }}
                onClick={() => setIsMenuOpen(false)}
              >
                Registrar
              </Link>
              <Link
                to="/database"
                style={{
                  ...linkStyle,
                  display: "block",
                  marginBottom: "0.5rem",
                }}
                onClick={() => setIsMenuOpen(false)}
              >
                Database
              </Link>
            </>
          )}
        </div>
      )}

      {/* CSS para responsividade */}
      <style jsx>{`
        @media (max-width: 768px) {
          .desktop-menu {
            display: none !important;
          }
          .mobile-hamburger {
            display: flex !important;
          }
        }
      `}</style>
    </nav>
  );
};

export default Navbar;
