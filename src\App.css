/* Layout principal */
.App {
  min-height: 100vh;
}

/* Conteúdo principal com padding para não ficar atrás da navbar */
.main-content {
  padding: 2rem 1rem;
  max-width: 1200px;
  margin: 0 auto;
}

/* Estilos básicos para formulários */
form {
  max-width: 500px;
  margin: 0 auto;
}

form div {
  margin-bottom: 1rem;
}

form label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: bold;
}

form input,
form textarea,
form select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1rem;
}

form button {
  background-color: #007bff;
  color: white;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
}

form button:hover {
  background-color: #0056b3;
}

/* Estilos para botões */
.btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  text-decoration: none;
  display: inline-block;
  font-size: 0.9rem;
}

.btn-primary {
  background-color: #007bff;
  color: white;
}

.btn-danger {
  background-color: #dc3545;
  color: white;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

/* Responsividade básica */
@media (max-width: 768px) {
  .main-content {
    padding: 1rem 0.5rem;
  }
}
