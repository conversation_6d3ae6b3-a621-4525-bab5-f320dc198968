import React, { useState, useEffect } from "react";
import { useAuth } from "../context/AuthContext";
import { useGames } from "../hooks/useGames";

const Library = () => {
  const {
    user,
    removeGameFromUser,
    updateUserGame,
    toggleGameCompleted,
    refreshTrigger,
  } = useAuth();
  const { games, loading, refreshGames } = useGames();
  const [filter, setFilter] = useState("all");
  const [editingGame, setEditingGame] = useState(null);

  // Recarregar jogos quando houver mudanças
  useEffect(() => {
    refreshGames();
  }, [refreshTrigger, refreshGames]);

  const deleteGame = async (gameId) => {
    if (window.confirm("Tem certeza que deseja remover este jogo?")) {
      try {
        const result = await removeGameFromUser(gameId);
        if (result.success) {
          // O refreshTrigger no contexto já vai atualizar automaticamente
          console.log("Jogo removido com sucesso!");
        }
      } catch (error) {
        console.error("Erro ao remover jogo:", error);
      }
    }
  };

  const editGameDate = async (gameId, newDate) => {
    try {
      const result = await updateUserGame(gameId, { release_date: newDate });
      if (result.success) {
        setEditingGame(null);
        // O refreshTrigger no contexto já vai atualizar automaticamente
        console.log("Data editada com sucesso!");
      }
    } catch (error) {
      console.error("Erro ao editar data:", error);
    }
  };

  const handleToggleCompleted = async (gameId) => {
    try {
      const result = await toggleGameCompleted(gameId);
      if (result.success) {
        // O refreshTrigger no contexto já vai atualizar automaticamente
        console.log("Status alterado com sucesso!");
      }
    } catch (error) {
      console.error("Erro ao alterar status:", error);
    }
  };

  const clearLibrary = () => {
    if (window.confirm("Tem certeza que deseja limpar toda a biblioteca?")) {
      setGames([]);
      localStorage.removeItem("gamesDatabase");
    }
  };

  const filteredGames = games.filter((game) => {
    if (filter === "all") return true;
    if (filter === "steam") return game.source === "steam";
    if (filter === "manual") return game.source === "manual";
    if (filter === "completed") return game.completed === true;
    if (filter === "pending") return game.completed !== true;
    return true;
  });

  if (!user) {
    return (
      <div>
        <h1>Acesso Negado</h1>
        <p>Você precisa estar logado para ver a biblioteca.</p>
      </div>
    );
  }

  return (
    <div className="main-content">
      <h1>📚 Biblioteca de Jogos</h1>

      {loading && (
        <div style={{ textAlign: "center", margin: "20px 0" }}>
          <p>🔄 Carregando jogos...</p>
        </div>
      )}

      <div style={{ marginBottom: "20px" }}>
        <p>Total de jogos: {games.length}</p>

        {/* Filtros */}
        <div style={{ marginBottom: "15px" }}>
          <label>Filtrar por: </label>
          <select
            value={filter}
            onChange={(e) => setFilter(e.target.value)}
            style={{ padding: "5px", marginLeft: "10px" }}
          >
            <option value="all">Todos os jogos</option>
            <option value="steam">Da Steam</option>
            <option value="manual">Adicionados manualmente</option>
            <option value="completed">Completados</option>
            <option value="pending">Pendentes</option>
          </select>
        </div>

        <button
          onClick={clearLibrary}
          style={{
            padding: "8px 16px",
            backgroundColor: "#dc3545",
            color: "white",
            border: "none",
            borderRadius: "4px",
            cursor: "pointer",
          }}
        >
          Limpar Biblioteca
        </button>
      </div>

      {filteredGames.length === 0 ? (
        <div style={{ textAlign: "center", padding: "40px", color: "#666" }}>
          <p>Nenhum jogo encontrado</p>
          <p>Adicione jogos usando a página "Adicionar Jogos"</p>
        </div>
      ) : (
        <div style={{ display: "grid", gap: "20px" }}>
          {filteredGames.map((game) => (
            <div
              key={game.id}
              style={{
                border: "1px solid #ccc",
                borderRadius: "8px",
                padding: "15px",
                display: "flex",
                gap: "15px",
              }}
            >
              <img
                src={game.image_url}
                alt={game.name}
                style={{
                  width: "184px",
                  height: "69px",
                  objectFit: "cover",
                  borderRadius: "4px",
                }}
                onError={(e) => {
                  e.target.src =
                    "https://via.placeholder.com/184x69?text=No+Image";
                }}
              />
              <div style={{ flex: 1 }}>
                <h3
                  style={{
                    display: "flex",
                    alignItems: "center",
                    gap: "0.5rem",
                  }}
                >
                  {game.completed && (
                    <span style={{ color: "#28a745" }}>✅</span>
                  )}
                  {game.name}
                </h3>
                <p>
                  <strong>Desenvolvedor:</strong> {game.developer}
                </p>
                {game.publisher && (
                  <p>
                    <strong>Publisher:</strong> {game.publisher}
                  </p>
                )}
                {game.genre && (
                  <p>
                    <strong>Gênero:</strong> {game.genre}
                  </p>
                )}
                {game.release_date && (
                  <p>
                    <strong>Lançamento:</strong>{" "}
                    {new Date(game.release_date).toLocaleDateString("pt-BR")}
                  </p>
                )}
                {game.price && (
                  <p>
                    <strong>Preço:</strong> {game.price}
                  </p>
                )}
                {game.rating && (
                  <p>
                    <strong>Avaliação:</strong> ⭐ {game.rating}/5
                  </p>
                )}

                <div style={{ marginTop: "10px" }}>
                  <span
                    style={{
                      backgroundColor:
                        game.source === "steam" ? "#1b2838" : "#6c757d",
                      color: "white",
                      padding: "2px 8px",
                      borderRadius: "12px",
                      fontSize: "12px",
                      marginRight: "10px",
                    }}
                  >
                    {game.source === "steam" ? "Steam" : "Manual"}
                  </span>
                  <span style={{ fontSize: "12px", color: "#666" }}>
                    Adicionado por {game.added_by} em{" "}
                    {new Date(game.added_date).toLocaleDateString("pt-BR")}
                  </span>
                </div>

                {game.tags && (
                  <div style={{ marginTop: "10px" }}>
                    {game.tags.map((tag, index) => (
                      <span
                        key={index}
                        style={{
                          backgroundColor: "#e9ecef",
                          padding: "2px 8px",
                          marginRight: "5px",
                          borderRadius: "12px",
                          fontSize: "12px",
                        }}
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                )}

                {game.description && (
                  <p
                    style={{
                      marginTop: "10px",
                      color: "#666",
                      fontSize: "14px",
                    }}
                  >
                    {game.description}
                  </p>
                )}

                <div
                  style={{ marginTop: "10px", display: "flex", gap: "10px" }}
                >
                  {editingGame === game.id ? (
                    <div
                      style={{
                        display: "flex",
                        gap: "5px",
                        alignItems: "center",
                      }}
                    >
                      <input
                        type="date"
                        defaultValue={game.release_date}
                        style={{ padding: "4px", fontSize: "12px" }}
                        onKeyPress={(e) => {
                          if (e.key === "Enter") {
                            editGameDate(game.id, e.target.value);
                          }
                        }}
                      />
                      <button
                        onClick={(e) => {
                          const input =
                            e.target.parentElement.querySelector("input");
                          editGameDate(game.id, input.value);
                        }}
                        style={{
                          padding: "4px 8px",
                          backgroundColor: "#28a745",
                          color: "white",
                          border: "none",
                          borderRadius: "4px",
                          cursor: "pointer",
                          fontSize: "12px",
                        }}
                      >
                        ✓
                      </button>
                      <button
                        onClick={() => setEditingGame(null)}
                        style={{
                          padding: "4px 8px",
                          backgroundColor: "#6c757d",
                          color: "white",
                          border: "none",
                          borderRadius: "4px",
                          cursor: "pointer",
                          fontSize: "12px",
                        }}
                      >
                        ✗
                      </button>
                    </div>
                  ) : (
                    <button
                      onClick={() => setEditingGame(game.id)}
                      style={{
                        padding: "6px 12px",
                        backgroundColor: "#007bff",
                        color: "white",
                        border: "none",
                        borderRadius: "4px",
                        cursor: "pointer",
                        fontSize: "12px",
                      }}
                    >
                      📅 Editar Data
                    </button>
                  )}

                  <button
                    onClick={() => handleToggleCompleted(game.id)}
                    style={{
                      padding: "6px 12px",
                      backgroundColor: game.completed ? "#28a745" : "#ffc107",
                      color: game.completed ? "white" : "black",
                      border: "none",
                      borderRadius: "4px",
                      cursor: "pointer",
                      fontSize: "12px",
                    }}
                  >
                    {game.completed ? "✅ Completado" : "⏳ Marcar Completo"}
                  </button>

                  <button
                    onClick={() => deleteGame(game.id)}
                    style={{
                      padding: "6px 12px",
                      backgroundColor: "#dc3545",
                      color: "white",
                      border: "none",
                      borderRadius: "4px",
                      cursor: "pointer",
                      fontSize: "12px",
                    }}
                  >
                    🗑️ Remover
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default Library;
