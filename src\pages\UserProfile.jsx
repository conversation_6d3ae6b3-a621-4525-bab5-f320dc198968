import React, { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { useAuth } from "../context/AuthContext";

const UserProfile = () => {
  const { username } = useParams();
  const navigate = useNavigate();
  const { getAllUsers, getUserGames } = useAuth();
  const [profileUser, setProfileUser] = useState(null);
  const [userGames, setUserGames] = useState([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("games");

  useEffect(() => {
    const loadUserProfile = async () => {
      setLoading(true);
      try {
        const users = await getAllUsers();
        const user = users.find((u) => u.username === username);

        if (!user) {
          navigate("/");
          return;
        }

        setProfileUser(user);

        // Carregar jogos do usuário
        const games = await getUserGames(user.id);
        setUserGames(games);
      } catch (error) {
        console.error("Erro ao carregar perfil:", error);
        navigate("/");
      } finally {
        setLoading(false);
      }
    };

    loadUserProfile();
  }, [username, getAllUsers, getUserGames, navigate]);

  if (loading) {
    return (
      <div className="main-content">
        <div style={{ textAlign: "center", padding: "2rem" }}>
          <h2>🔄 Carregando perfil...</h2>
        </div>
      </div>
    );
  }

  if (!profileUser) {
    return (
      <div className="main-content">
        <div style={{ textAlign: "center", padding: "2rem" }}>
          <h2>❌ Usuário não encontrado</h2>
          <button onClick={() => navigate("/")} style={{ marginTop: "1rem" }}>
            Voltar ao Início
          </button>
        </div>
      </div>
    );
  }

  const completedGames = userGames.filter((game) => game.completed);
  const totalGames = userGames.length;

  return (
    <div className="main-content">
      {/* Header do Perfil */}
      <div
        style={{
          position: "relative",
          marginBottom: "2rem",
          borderRadius: "12px",
          overflow: "hidden",
          minHeight: "200px",
          ...(profileUser.banner && {
            backgroundImage:
              profileUser.banner.includes(".mp4") ||
              profileUser.banner.includes(".webm") ||
              profileUser.banner.includes(".mov")
                ? "none"
                : `url(${profileUser.banner})`,
            backgroundSize: "cover",
            backgroundPosition: "center",
            backgroundRepeat: "no-repeat",
          }),
          backgroundColor: profileUser.banner ? "transparent" : "#f8f9fa",
          border: "1px solid #dee2e6",
        }}
      >
        {/* Banner de vídeo se for mídia */}
        {profileUser.banner &&
          (profileUser.banner.includes(".mp4") ||
            profileUser.banner.includes(".webm") ||
            profileUser.banner.includes(".mov")) && (
            <video
              autoPlay
              loop
              muted
              style={{
                position: "absolute",
                top: 0,
                left: 0,
                width: "100%",
                height: "100%",
                objectFit: "cover",
                zIndex: 0,
              }}
            >
              <source src={profileUser.banner} type="video/mp4" />
            </video>
          )}

        {/* Overlay */}
        {profileUser.banner && (
          <div
            style={{
              position: "absolute",
              top: 0,
              left: 0,
              width: "100%",
              height: "100%",
              background:
                "linear-gradient(to bottom, rgba(0,0,0,0.3), rgba(0,0,0,0.7))",
              zIndex: 1,
            }}
          />
        )}

        {/* Conteúdo do Header */}
        <div
          style={{
            position: "relative",
            zIndex: 2,
            padding: "2rem",
            display: "flex",
            alignItems: "flex-end",
            minHeight: "200px",
          }}
        >
          <div style={{ display: "flex", alignItems: "center", gap: "1.5rem" }}>
            <img
              src={profileUser.avatar}
              alt={profileUser.name}
              style={{
                width: "100px",
                height: "100px",
                borderRadius: "50%",
                border: "4px solid white",
                boxShadow: "0 4px 8px rgba(0,0,0,0.3)",
              }}
            />
            <div>
              <h1
                style={{
                  margin: "0 0 0.5rem 0",
                  color: profileUser.banner ? "white" : "#333",
                  textShadow: profileUser.banner
                    ? "2px 2px 4px rgba(0,0,0,0.8)"
                    : "none",
                  fontSize: "2.5rem",
                }}
              >
                {profileUser.name}
              </h1>
              <p
                style={{
                  margin: "0 0 0.5rem 0",
                  color: profileUser.banner ? "#f8f9fa" : "#666",
                  textShadow: profileUser.banner
                    ? "1px 1px 2px rgba(0,0,0,0.8)"
                    : "none",
                  fontSize: "1.2rem",
                }}
              >
                @{profileUser.username}
              </p>
              {profileUser.favoriteCompany && (
                <p
                  style={{
                    margin: "0",
                    color: profileUser.banner ? "#f8f9fa" : "#666",
                    textShadow: profileUser.banner
                      ? "1px 1px 2px rgba(0,0,0,0.8)"
                      : "none",
                  }}
                >
                  💼 {profileUser.favoriteCompany}
                </p>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Estatísticas */}
      <div
        style={{
          display: "grid",
          gridTemplateColumns: "repeat(auto-fit, minmax(200px, 1fr))",
          gap: "1rem",
          marginBottom: "2rem",
        }}
      >
        <div
          style={{
            backgroundColor: "#007bff",
            color: "white",
            padding: "1.5rem",
            borderRadius: "8px",
            textAlign: "center",
          }}
        >
          <div style={{ fontSize: "2rem", fontWeight: "bold" }}>
            {totalGames}
          </div>
          <div>Jogos Total</div>
        </div>
        <div
          style={{
            backgroundColor: "#28a745",
            color: "white",
            padding: "1.5rem",
            borderRadius: "8px",
            textAlign: "center",
          }}
        >
          <div style={{ fontSize: "2rem", fontWeight: "bold" }}>
            {completedGames.length}
          </div>
          <div>Completados</div>
        </div>
        {profileUser.pcRank && (
          <div
            style={{
              backgroundColor: "#6f42c1",
              color: "white",
              padding: "1.5rem",
              borderRadius: "8px",
              textAlign: "center",
            }}
          >
            <div style={{ fontSize: "2rem" }}>{profileUser.pcRank.emoji}</div>
            <div>{profileUser.pcRank.rank}</div>
            <div style={{ fontSize: "0.9rem", opacity: 0.9 }}>
              {profileUser.pcRank.points} pts
            </div>
          </div>
        )}
      </div>

      {/* Tabs */}
      <div style={{ marginBottom: "2rem" }}>
        <div
          style={{
            display: "flex",
            borderBottom: "2px solid #dee2e6",
            gap: "1rem",
          }}
        >
          <button
            onClick={() => setActiveTab("games")}
            style={{
              padding: "1rem 1.5rem",
              border: "none",
              background: "none",
              borderBottom:
                activeTab === "games"
                  ? "3px solid #007bff"
                  : "3px solid transparent",
              color: activeTab === "games" ? "#007bff" : "#666",
              fontWeight: activeTab === "games" ? "bold" : "normal",
              cursor: "pointer",
            }}
          >
            🎮 Jogos ({totalGames})
          </button>
          <button
            onClick={() => setActiveTab("completed")}
            style={{
              padding: "1rem 1.5rem",
              border: "none",
              background: "none",
              borderBottom:
                activeTab === "completed"
                  ? "3px solid #007bff"
                  : "3px solid transparent",
              color: activeTab === "completed" ? "#007bff" : "#666",
              fontWeight: activeTab === "completed" ? "bold" : "normal",
              cursor: "pointer",
            }}
          >
            ✅ Completados ({completedGames.length})
          </button>
          {profileUser.pcRank && (
            <button
              onClick={() => setActiveTab("pc")}
              style={{
                padding: "1rem 1.5rem",
                border: "none",
                background: "none",
                borderBottom:
                  activeTab === "pc"
                    ? "3px solid #007bff"
                    : "3px solid transparent",
                color: activeTab === "pc" ? "#007bff" : "#666",
                fontWeight: activeTab === "pc" ? "bold" : "normal",
                cursor: "pointer",
              }}
            >
              💻 PC Setup
            </button>
          )}
        </div>
      </div>

      {/* Conteúdo das Tabs */}
      {activeTab === "games" && (
        <div>
          <h3>🎮 Biblioteca de Jogos</h3>
          {userGames.length > 0 ? (
            <div
              style={{
                display: "grid",
                gridTemplateColumns: "repeat(auto-fill, minmax(400px, 1fr))",
                gap: "1rem",
              }}
            >
              {userGames.map((game) => (
                <div
                  key={game.id}
                  style={{
                    backgroundColor: "#f8f9fa",
                    border: "1px solid #dee2e6",
                    borderRadius: "8px",
                    padding: "1rem",
                    display: "flex",
                    alignItems: "center",
                    gap: "1rem",
                  }}
                >
                  <img
                    src={
                      game.image_url ||
                      game.image ||
                      (game.steam_appid && game.steam_appid !== "unknown"
                        ? `https://cdn.akamai.steamstatic.com/steam/apps/${game.steam_appid}/header.jpg`
                        : "https://via.placeholder.com/184x69/6c757d/ffffff?text=Game")
                    }
                    alt={game.name}
                    onError={(e) => {
                      e.target.src =
                        "https://via.placeholder.com/184x69/6c757d/ffffff?text=Game";
                    }}
                    style={{
                      width: "184px",
                      height: "69px",
                      borderRadius: "8px",
                      objectFit: "cover",
                    }}
                  />
                  <div style={{ flex: 1 }}>
                    <h4 style={{ margin: "0 0 0.5rem 0", fontSize: "1rem" }}>
                      {game.name}
                    </h4>
                    <p
                      style={{ margin: "0", fontSize: "0.9rem", color: "#666" }}
                    >
                      {game.developer}
                    </p>
                    <div style={{ marginTop: "0.5rem" }}>
                      {game.completed ? (
                        <span
                          style={{
                            backgroundColor: "#28a745",
                            color: "white",
                            padding: "0.25rem 0.5rem",
                            borderRadius: "12px",
                            fontSize: "0.8rem",
                          }}
                        >
                          ✅ Completado
                        </span>
                      ) : (
                        <span
                          style={{
                            backgroundColor: "#ffc107",
                            color: "#333",
                            padding: "0.25rem 0.5rem",
                            borderRadius: "12px",
                            fontSize: "0.8rem",
                          }}
                        >
                          ⏳ Jogando
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p style={{ textAlign: "center", color: "#666", padding: "2rem" }}>
              Este usuário ainda não adicionou nenhum jogo.
            </p>
          )}
        </div>
      )}

      {activeTab === "completed" && (
        <div>
          <h3>✅ Jogos Completados</h3>
          {completedGames.length > 0 ? (
            <div
              style={{
                display: "grid",
                gridTemplateColumns: "repeat(auto-fill, minmax(400px, 1fr))",
                gap: "1rem",
              }}
            >
              {completedGames.map((game) => (
                <div
                  key={game.id}
                  style={{
                    backgroundColor: "#d4edda",
                    border: "1px solid #c3e6cb",
                    borderRadius: "8px",
                    padding: "1rem",
                    display: "flex",
                    alignItems: "center",
                    gap: "1rem",
                  }}
                >
                  <img
                    src={
                      game.image_url ||
                      game.image ||
                      (game.steam_appid && game.steam_appid !== "unknown"
                        ? `https://cdn.akamai.steamstatic.com/steam/apps/${game.steam_appid}/header.jpg`
                        : "https://via.placeholder.com/184x69/6c757d/ffffff?text=Game")
                    }
                    alt={game.name}
                    onError={(e) => {
                      e.target.src =
                        "https://via.placeholder.com/184x69/6c757d/ffffff?text=Game";
                    }}
                    style={{
                      width: "184px",
                      height: "69px",
                      borderRadius: "8px",
                      objectFit: "cover",
                    }}
                  />
                  <div style={{ flex: 1 }}>
                    <h4 style={{ margin: "0 0 0.5rem 0", fontSize: "1rem" }}>
                      {game.name}
                    </h4>
                    <p
                      style={{ margin: "0", fontSize: "0.9rem", color: "#666" }}
                    >
                      {game.developer}
                    </p>
                    <div style={{ marginTop: "0.5rem" }}>
                      <span
                        style={{
                          backgroundColor: "#28a745",
                          color: "white",
                          padding: "0.25rem 0.5rem",
                          borderRadius: "12px",
                          fontSize: "0.8rem",
                        }}
                      >
                        ✅ Completado
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p style={{ textAlign: "center", color: "#666", padding: "2rem" }}>
              Este usuário ainda não completou nenhum jogo.
            </p>
          )}
        </div>
      )}

      {activeTab === "pc" && profileUser.pcRank && (
        <div>
          <h3>💻 Configuração do PC</h3>
          <div
            style={{
              backgroundColor: "#f8f9fa",
              border: "1px solid #dee2e6",
              borderRadius: "8px",
              padding: "2rem",
            }}
          >
            <div
              style={{
                display: "grid",
                gridTemplateColumns: "repeat(auto-fit, minmax(250px, 1fr))",
                gap: "1.5rem",
              }}
            >
              <div>
                <h4 style={{ color: "#495057", marginBottom: "0.5rem" }}>
                  🎮 Placa de Vídeo
                </h4>
                <p style={{ margin: 0, fontSize: "1.1rem" }}>
                  {profileUser.pcRank.gpu || "Não informado"}
                </p>
              </div>
              <div>
                <h4 style={{ color: "#495057", marginBottom: "0.5rem" }}>
                  🧠 Processador
                </h4>
                <p style={{ margin: 0, fontSize: "1.1rem" }}>
                  {profileUser.pcRank.cpu || "Não informado"}
                </p>
              </div>
              <div>
                <h4 style={{ color: "#495057", marginBottom: "0.5rem" }}>
                  💾 Memória RAM
                </h4>
                <p style={{ margin: 0, fontSize: "1.1rem" }}>
                  {profileUser.pcRank.ram || "Não informado"}
                </p>
              </div>
              <div>
                <h4 style={{ color: "#495057", marginBottom: "0.5rem" }}>
                  💿 Armazenamento
                </h4>
                <p style={{ margin: 0, fontSize: "1.1rem" }}>
                  {profileUser.pcRank.storage || "Não informado"}
                </p>
              </div>
            </div>

            <div
              style={{
                marginTop: "2rem",
                textAlign: "center",
                padding: "1.5rem",
                backgroundColor: "#e9ecef",
                borderRadius: "8px",
              }}
            >
              <div style={{ fontSize: "3rem", marginBottom: "0.5rem" }}>
                {profileUser.pcRank.emoji}
              </div>
              <h3 style={{ margin: "0 0 0.5rem 0", color: "#495057" }}>
                {profileUser.pcRank.rank}
              </h3>
              <p style={{ margin: 0, color: "#6c757d" }}>
                {profileUser.pcRank.points} pontos totais
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Botão Voltar */}
      <div style={{ marginTop: "2rem", textAlign: "center" }}>
        <button
          onClick={() => navigate("/")}
          style={{
            backgroundColor: "#6c757d",
            color: "white",
            border: "none",
            padding: "0.75rem 1.5rem",
            borderRadius: "8px",
            cursor: "pointer",
            fontSize: "1rem",
          }}
        >
          ← Voltar ao Início
        </button>
      </div>
    </div>
  );
};

export default UserProfile;
