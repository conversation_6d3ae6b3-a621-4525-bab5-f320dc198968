import React, { createContext, useContext, useState, useEffect } from "react";

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  // Verificar se há usuário logado no localStorage
  useEffect(() => {
    const savedUser = localStorage.getItem("currentUser");
    if (savedUser) {
      setUser(JSON.parse(savedUser));
    }
    setLoading(false);
  }, []);

  // Função para obter banco de dados
  const getDatabase = () => {
    const db = localStorage.getItem("usersDatabase");
    return db ? JSON.parse(db) : [];
  };

  // Função para salvar no banco de dados
  const saveToDatabase = (users) => {
    localStorage.setItem("usersDatabase", JSON.stringify(users));
  };

  // Função de login
  const login = (username, password) => {
    // Verificar conta de administrador
    if (username === "admin" && password === "admin") {
      const adminUser = {
        id: 0,
        username: "admin",
        name: "Administrador",
        avatar:
          "https://ui-avatars.com/api/?name=Admin&background=dc3545&color=fff",
        createdAt: new Date().toISOString(),
        isAdmin: true,
      };

      setUser(adminUser);
      localStorage.setItem("currentUser", JSON.stringify(adminUser));

      // Log de login admin
      const logs = JSON.parse(localStorage.getItem("systemLogs") || "[]");
      const newLog = {
        id: Date.now(),
        timestamp: new Date().toISOString(),
        user: "admin",
        action: "ADMIN_LOGIN",
        details: "Administrador fez login no sistema",
      };
      logs.unshift(newLog);
      localStorage.setItem("systemLogs", JSON.stringify(logs));

      return { success: true };
    }

    const users = getDatabase();
    const foundUser = users.find(
      (u) => u.username === username && u.password === password
    );

    if (foundUser) {
      const userData = {
        id: foundUser.id,
        username: foundUser.username,
        name: foundUser.name,
        avatar: foundUser.avatar,
        createdAt: foundUser.createdAt,
        isAdmin: false,
        favoriteCompany: foundUser.favoriteCompany || "",
        banner: foundUser.banner || "",
        pcRank: foundUser.pcRank || {
          rank: "Bronze",
          emoji: "🥉",
          points: 0,
        },
      };

      setUser(userData);
      localStorage.setItem("currentUser", JSON.stringify(userData));

      // Log de login
      const logs = JSON.parse(localStorage.getItem("systemLogs") || "[]");
      const newLog = {
        id: Date.now(),
        timestamp: new Date().toISOString(),
        user: username,
        action: "USER_LOGIN",
        details: `Usuário ${username} fez login`,
      };
      logs.unshift(newLog);
      localStorage.setItem("systemLogs", JSON.stringify(logs));

      return { success: true };
    } else {
      return { success: false, message: "Usuário ou senha inválidos" };
    }
  };

  // Função de registro
  const register = (username, password, name) => {
    const users = getDatabase();

    // Verificar se usuário já existe
    const existingUser = users.find((u) => u.username === username);
    if (existingUser) {
      return { success: false, message: "Nome de usuário já existe" };
    }

    // Criar novo usuário
    const newUser = {
      id: Date.now(),
      username: username,
      password: password, // Em produção, seria hash da senha
      name: name,
      avatar: `https://ui-avatars.com/api/?name=${name}&background=random`,
      createdAt: new Date().toISOString(),
      favoriteCompany: "",
      banner: "",
      pcRank: {
        rank: "Bronze",
        emoji: "🥉",
        points: 0,
      },
    };

    // Adicionar ao banco de dados
    users.push(newUser);
    saveToDatabase(users);

    // Fazer login automático
    const userData = {
      id: newUser.id,
      username: newUser.username,
      name: newUser.name,
      avatar: newUser.avatar,
      createdAt: newUser.createdAt,
      isAdmin: false,
    };

    setUser(userData);
    localStorage.setItem("currentUser", JSON.stringify(userData));

    // Log de registro
    const logs = JSON.parse(localStorage.getItem("systemLogs") || "[]");
    const newLog = {
      id: Date.now() + 1, // +1 para evitar conflito de ID
      timestamp: new Date().toISOString(),
      user: username,
      action: "USER_REGISTER",
      details: `Novo usuário ${username} se registrou no sistema`,
    };
    logs.unshift(newLog);
    localStorage.setItem("systemLogs", JSON.stringify(logs));

    return { success: true };
  };

  // Função de logout
  const logout = () => {
    setUser(null);
    localStorage.removeItem("currentUser");
  };

  // Função para obter todos os usuários (para debug)
  const getAllUsers = () => {
    return getDatabase();
  };

  // Função para adicionar log
  const addLog = (action, details) => {
    const logs = JSON.parse(localStorage.getItem("systemLogs") || "[]");
    const newLog = {
      id: Date.now(),
      timestamp: new Date().toISOString(),
      user: user?.username || "Anônimo",
      action: action,
      details: details,
    };
    logs.unshift(newLog); // Adiciona no início
    // Manter apenas os últimos 100 logs
    if (logs.length > 100) {
      logs.splice(100);
    }
    localStorage.setItem("systemLogs", JSON.stringify(logs));
  };

  // Função para obter logs
  const getLogs = () => {
    return JSON.parse(localStorage.getItem("systemLogs") || "[]");
  };

  // Função para atualizar perfil do usuário
  const updateProfile = (updatedData) => {
    const users = getDatabase();
    const userIndex = users.findIndex((u) => u.id === user.id);

    if (userIndex !== -1) {
      // Atualizar no banco de dados
      users[userIndex] = { ...users[userIndex], ...updatedData };
      saveToDatabase(users);

      // Atualizar usuário atual
      const updatedUser = { ...user, ...updatedData };
      setUser(updatedUser);
      localStorage.setItem("currentUser", JSON.stringify(updatedUser));

      addLog(
        "PROFILE_UPDATE",
        `Perfil atualizado: ${Object.keys(updatedData).join(", ")}`
      );
      return { success: true };
    }

    return { success: false, message: "Usuário não encontrado" };
  };

  const value = {
    user,
    login,
    register,
    logout,
    loading,
    getAllUsers,
    addLog,
    getLogs,
    updateProfile,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
