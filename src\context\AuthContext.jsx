import React, { createContext, useContext, useState, useEffect } from "react";

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  // Verificar se há usuário logado no localStorage
  useEffect(() => {
    const savedUser = localStorage.getItem("currentUser");
    if (savedUser) {
      setUser(JSON.parse(savedUser));
    }
    setLoading(false);
  }, []);

  // Função para obter banco de dados
  const getDatabase = () => {
    const db = localStorage.getItem("usersDatabase");
    return db ? JSON.parse(db) : [];
  };

  // Função para salvar no banco de dados
  const saveToDatabase = (users) => {
    localStorage.setItem("usersDatabase", JSON.stringify(users));
  };

  // Função de login
  const login = (username, password) => {
    const users = getDatabase();
    const foundUser = users.find(
      (u) => u.username === username && u.password === password
    );

    if (foundUser) {
      const userData = {
        id: foundUser.id,
        username: foundUser.username,
        name: foundUser.name,
        avatar: foundUser.avatar,
        createdAt: foundUser.createdAt,
      };

      setUser(userData);
      localStorage.setItem("currentUser", JSON.stringify(userData));
      return { success: true };
    } else {
      return { success: false, message: "Usuário ou senha inválidos" };
    }
  };

  // Função de registro
  const register = (username, password, name) => {
    const users = getDatabase();

    // Verificar se usuário já existe
    const existingUser = users.find((u) => u.username === username);
    if (existingUser) {
      return { success: false, message: "Nome de usuário já existe" };
    }

    // Criar novo usuário
    const newUser = {
      id: Date.now(),
      username: username,
      password: password, // Em produção, seria hash da senha
      name: name,
      avatar: `https://ui-avatars.com/api/?name=${name}&background=random`,
      createdAt: new Date().toISOString(),
    };

    // Adicionar ao banco de dados
    users.push(newUser);
    saveToDatabase(users);

    // Fazer login automático
    const userData = {
      id: newUser.id,
      username: newUser.username,
      name: newUser.name,
      avatar: newUser.avatar,
      createdAt: newUser.createdAt,
    };

    setUser(userData);
    localStorage.setItem("currentUser", JSON.stringify(userData));
    return { success: true };
  };

  // Função de logout
  const logout = () => {
    setUser(null);
    localStorage.removeItem("currentUser");
  };

  // Função para obter todos os usuários (para debug)
  const getAllUsers = () => {
    return getDatabase();
  };

  const value = {
    user,
    login,
    register,
    logout,
    loading,
    getAllUsers,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
