import React, { createContext, useContext, useState, useEffect } from "react";
import databaseService from "../services/DatabaseService";

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  // Verificar se há usuário logado no localStorage
  useEffect(() => {
    const initializeApp = async () => {
      // Carregar usuário salvo
      const savedUser = localStorage.getItem("currentUser");
      if (savedUser) {
        setUser(JSON.parse(savedUser));
      }

      // Migrar dados do localStorage para o servidor
      await migrateLocalDataToServer();

      setLoading(false);
    };

    initializeApp();
  }, []);

  // Função para migrar dados do localStorage para o servidor
  const migrateLocalDataToServer = async () => {
    try {
      // Verificar se já foi migrado
      const migrated = localStorage.getItem("dataMigrated");
      if (migrated === "true") {
        console.log("Dados já foram migrados anteriormente.");
        return;
      }

      // Migrar usuários
      const localUsers = JSON.parse(
        localStorage.getItem("usersDatabase") || "[]"
      );
      if (localUsers.length > 0) {
        console.log("Migrando usuários para o servidor...");

        // Obter usuários existentes no servidor
        const existingUsers = await databaseService.getUsers();

        for (const user of localUsers) {
          // Verificar se usuário já existe (por username)
          const userExists = existingUsers.find(
            (u) => u.username === user.username
          );

          if (!userExists) {
            try {
              await databaseService.createUser(user);
              console.log(`Usuário ${user.username} migrado com sucesso`);
            } catch (error) {
              console.log("Erro ao migrar usuário:", error.message);
            }
          } else {
            console.log(`Usuário ${user.username} já existe no servidor`);
          }
        }
      }

      // Migrar jogos
      const localGames = JSON.parse(
        localStorage.getItem("gamesDatabase") || "[]"
      );
      if (localGames.length > 0) {
        console.log("Migrando jogos para o servidor...");

        // Obter jogos existentes no servidor
        const existingGames = await databaseService.getGames();

        for (const game of localGames) {
          // Verificar se jogo já existe (por nome e usuário)
          const gameExists = existingGames.find(
            (g) => g.name === game.name && g.added_by_id === game.added_by_id
          );

          if (!gameExists) {
            try {
              await databaseService.createGame(game);
              console.log(`Jogo ${game.name} migrado com sucesso`);
            } catch (error) {
              console.log("Erro ao migrar jogo:", error.message);
            }
          } else {
            console.log(`Jogo ${game.name} já existe no servidor`);
          }
        }
      }

      // Migrar logs
      const localLogs = JSON.parse(localStorage.getItem("systemLogs") || "[]");
      if (localLogs.length > 0) {
        console.log("Migrando logs para o servidor...");

        // Obter logs existentes no servidor
        const existingLogs = await databaseService.getLogs();

        for (const log of localLogs) {
          // Verificar se log já existe (por timestamp e usuário)
          const logExists = existingLogs.find(
            (l) => l.timestamp === log.timestamp && l.user === log.user
          );

          if (!logExists) {
            try {
              await databaseService.createLog(log);
              console.log(`Log migrado com sucesso`);
            } catch (error) {
              console.log("Erro ao migrar log:", error.message);
            }
          }
        }
      }

      // Marcar como migrado para não repetir
      localStorage.setItem("dataMigrated", "true");
      console.log("Migração concluída com sucesso!");
    } catch (error) {
      console.error("Erro na migração:", error);
    }
  };

  // Função para obter banco de dados
  const getDatabase = async () => {
    try {
      return await databaseService.getUsers();
    } catch (error) {
      console.error("Erro ao obter banco de dados:", error);
      const db = localStorage.getItem("usersDatabase");
      return db ? JSON.parse(db) : [];
    }
  };

  // Função para salvar no banco de dados
  const saveToDatabase = async (users) => {
    try {
      // Salvar no servidor
      for (const user of users) {
        if (!user.id) {
          await databaseService.createUser(user);
        } else {
          await databaseService.updateUser(user.id, user);
        }
      }
    } catch (error) {
      console.error("Erro ao salvar no servidor:", error);
      // Fallback para localStorage
      localStorage.setItem("usersDatabase", JSON.stringify(users));
    }
  };

  // Função de login
  const login = async (username, password) => {
    // Verificar conta de administrador
    if (username === "admin" && password === "admin") {
      const adminUser = {
        id: 0,
        username: "admin",
        name: "Administrador",
        avatar:
          "https://ui-avatars.com/api/?name=Admin&background=dc3545&color=fff",
        createdAt: new Date().toISOString(),
        isAdmin: true,
      };

      setUser(adminUser);
      localStorage.setItem("currentUser", JSON.stringify(adminUser));

      // Log de login admin
      const logs = JSON.parse(localStorage.getItem("systemLogs") || "[]");
      const newLog = {
        id: Date.now(),
        timestamp: new Date().toISOString(),
        user: "admin",
        action: "ADMIN_LOGIN",
        details: "Administrador fez login no sistema",
      };
      logs.unshift(newLog);
      localStorage.setItem("systemLogs", JSON.stringify(logs));

      return { success: true };
    }

    const users = await getDatabase();
    const foundUser = users.find(
      (u) => u.username === username && u.password === password
    );

    if (foundUser) {
      const userData = {
        id: foundUser.id,
        username: foundUser.username,
        name: foundUser.name,
        avatar: foundUser.avatar,
        createdAt: foundUser.createdAt,
        isAdmin: false,
        favoriteCompany: foundUser.favoriteCompany || "",
        banner: foundUser.banner || "",
        pcSpecs: foundUser.pcSpecs || {
          gpu: "",
          cpu: "",
          ram: "",
          storage: "",
        },
        pcRank: foundUser.pcRank || {
          rank: "Bronze",
          emoji: "🥉",
          points: 0,
        },
      };

      setUser(userData);
      localStorage.setItem("currentUser", JSON.stringify(userData));

      // Log de login
      const logs = JSON.parse(localStorage.getItem("systemLogs") || "[]");
      const newLog = {
        id: Date.now(),
        timestamp: new Date().toISOString(),
        user: username,
        action: "USER_LOGIN",
        details: `Usuário ${username} fez login`,
      };
      logs.unshift(newLog);
      localStorage.setItem("systemLogs", JSON.stringify(logs));

      return { success: true };
    } else {
      return { success: false, message: "Usuário ou senha inválidos" };
    }
  };

  // Função de registro
  const register = async (username, password, name) => {
    const users = await getDatabase();

    // Verificar se usuário já existe
    const existingUser = users.find((u) => u.username === username);
    if (existingUser) {
      return { success: false, message: "Nome de usuário já existe" };
    }

    // Criar novo usuário
    const newUser = {
      id: Date.now(),
      username: username,
      password: password, // Em produção, seria hash da senha
      name: name,
      avatar: `https://ui-avatars.com/api/?name=${name}&background=random`,
      createdAt: new Date().toISOString(),
      favoriteCompany: "",
      banner: "",
      pcSpecs: {
        gpu: "",
        cpu: "",
        ram: "",
        storage: "",
      },
      pcRank: {
        rank: "Bronze",
        emoji: "🥉",
        points: 0,
      },
    };

    // Adicionar ao banco de dados
    try {
      await databaseService.createUser(newUser);
    } catch (error) {
      console.error("Erro ao criar usuário no servidor:", error);
      users.push(newUser);
      await saveToDatabase(users);
    }

    // Fazer login automático
    const userData = {
      id: newUser.id,
      username: newUser.username,
      name: newUser.name,
      avatar: newUser.avatar,
      createdAt: newUser.createdAt,
      isAdmin: false,
    };

    setUser(userData);
    localStorage.setItem("currentUser", JSON.stringify(userData));

    // Log de registro
    const logs = JSON.parse(localStorage.getItem("systemLogs") || "[]");
    const newLog = {
      id: Date.now() + 1, // +1 para evitar conflito de ID
      timestamp: new Date().toISOString(),
      user: username,
      action: "USER_REGISTER",
      details: `Novo usuário ${username} se registrou no sistema`,
    };
    logs.unshift(newLog);
    localStorage.setItem("systemLogs", JSON.stringify(logs));

    return { success: true };
  };

  // Função de logout
  const logout = () => {
    setUser(null);
    localStorage.removeItem("currentUser");
  };

  // Função para obter todos os usuários (para debug)
  const getAllUsers = () => {
    return getDatabase();
  };

  // Função para adicionar log
  const addLog = (action, details) => {
    const logs = JSON.parse(localStorage.getItem("systemLogs") || "[]");
    const newLog = {
      id: Date.now(),
      timestamp: new Date().toISOString(),
      user: user?.username || "Anônimo",
      action: action,
      details: details,
    };
    logs.unshift(newLog); // Adiciona no início
    // Manter apenas os últimos 100 logs
    if (logs.length > 100) {
      logs.splice(100);
    }
    localStorage.setItem("systemLogs", JSON.stringify(logs));
  };

  // Função para obter logs
  const getLogs = () => {
    return JSON.parse(localStorage.getItem("systemLogs") || "[]");
  };

  // Função para atualizar perfil do usuário
  const updateProfile = (updatedData) => {
    const users = getDatabase();
    const userIndex = users.findIndex((u) => u.id === user.id);

    if (userIndex !== -1) {
      // Atualizar no banco de dados
      users[userIndex] = { ...users[userIndex], ...updatedData };
      saveToDatabase(users);

      // Atualizar usuário atual
      const updatedUser = { ...user, ...updatedData };
      setUser(updatedUser);
      localStorage.setItem("currentUser", JSON.stringify(updatedUser));

      addLog(
        "PROFILE_UPDATE",
        `Perfil atualizado: ${Object.keys(updatedData).join(", ")}`
      );
      return { success: true };
    }

    return { success: false, message: "Usuário não encontrado" };
  };

  // Função para calcular PC Rank baseado nas especificações
  const calculatePcRank = (pcSpecs) => {
    let totalPoints = 0;

    // Pontuação para GPU
    const gpuPoints = getGpuPoints(pcSpecs.gpu);
    totalPoints += gpuPoints;

    // Pontuação para CPU
    const cpuPoints = getCpuPoints(pcSpecs.cpu);
    totalPoints += cpuPoints;

    // Pontuação para RAM
    const ramPoints = getRamPoints(pcSpecs.ram);
    totalPoints += ramPoints;

    // Pontuação para Storage
    const storagePoints = getStoragePoints(pcSpecs.storage);
    totalPoints += storagePoints;

    // Determinar rank baseado na pontuação total
    if (totalPoints >= 300) {
      return { rank: "Diamante", emoji: "💎", points: totalPoints };
    } else if (totalPoints >= 200) {
      return { rank: "Ouro", emoji: "🥇", points: totalPoints };
    } else if (totalPoints >= 100) {
      return { rank: "Prata", emoji: "🥈", points: totalPoints };
    } else if (totalPoints >= 50) {
      return { rank: "Bronze", emoji: "🥉", points: totalPoints };
    } else {
      return { rank: "Lixo", emoji: "💩", points: totalPoints };
    }
  };

  // Função para calcular pontos da GPU
  const getGpuPoints = (gpu) => {
    const gpuLower = gpu.toLowerCase();

    // RTX 40 Series
    if (gpuLower.includes("rtx 4090")) return 100;
    if (gpuLower.includes("rtx 4080")) return 95;
    if (gpuLower.includes("rtx 4070")) return 85;
    if (gpuLower.includes("rtx 4060")) return 75;

    // RTX 30 Series
    if (gpuLower.includes("rtx 3090")) return 90;
    if (gpuLower.includes("rtx 3080")) return 85;
    if (gpuLower.includes("rtx 3070")) return 75;
    if (gpuLower.includes("rtx 3060")) return 65;

    // RTX 20 Series
    if (gpuLower.includes("rtx 2080")) return 70;
    if (gpuLower.includes("rtx 2070")) return 60;
    if (gpuLower.includes("rtx 2060")) return 50;

    // GTX Series
    if (gpuLower.includes("gtx 1080")) return 55;
    if (gpuLower.includes("gtx 1070")) return 45;
    if (gpuLower.includes("gtx 1060")) return 35;
    if (gpuLower.includes("gtx 1050")) return 25;

    // AMD RX Series
    if (gpuLower.includes("rx 7900")) return 95;
    if (gpuLower.includes("rx 7800")) return 85;
    if (gpuLower.includes("rx 6900")) return 80;
    if (gpuLower.includes("rx 6800")) return 75;
    if (gpuLower.includes("rx 6700")) return 65;
    if (gpuLower.includes("rx 6600")) return 55;
    if (gpuLower.includes("rx 580")) return 35;
    if (gpuLower.includes("rx 570")) return 30;

    return 10; // GPU básica ou não reconhecida
  };

  // Função para calcular pontos do CPU
  const getCpuPoints = (cpu) => {
    const cpuLower = cpu.toLowerCase();

    // Intel 13th Gen
    if (cpuLower.includes("i9-13900")) return 100;
    if (cpuLower.includes("i7-13700")) return 85;
    if (cpuLower.includes("i5-13600")) return 70;
    if (cpuLower.includes("i5-13400")) return 60;

    // Intel 12th Gen
    if (cpuLower.includes("i9-12900")) return 90;
    if (cpuLower.includes("i7-12700")) return 75;
    if (cpuLower.includes("i5-12600")) return 65;
    if (cpuLower.includes("i5-12400")) return 55;

    // Intel 11th Gen
    if (cpuLower.includes("i9-11900")) return 80;
    if (cpuLower.includes("i7-11700")) return 65;
    if (cpuLower.includes("i5-11600")) return 55;
    if (cpuLower.includes("i5-11400")) return 45;

    // AMD Ryzen 7000 Series
    if (cpuLower.includes("ryzen 9 7950")) return 100;
    if (cpuLower.includes("ryzen 9 7900")) return 95;
    if (cpuLower.includes("ryzen 7 7700")) return 80;
    if (cpuLower.includes("ryzen 5 7600")) return 70;

    // AMD Ryzen 5000 Series
    if (cpuLower.includes("ryzen 9 5950")) return 90;
    if (cpuLower.includes("ryzen 9 5900")) return 85;
    if (cpuLower.includes("ryzen 7 5800")) return 75;
    if (cpuLower.includes("ryzen 5 5600")) return 65;

    // AMD Ryzen 3000 Series
    if (cpuLower.includes("ryzen 9 3950")) return 75;
    if (cpuLower.includes("ryzen 9 3900")) return 70;
    if (cpuLower.includes("ryzen 7 3700")) return 60;
    if (cpuLower.includes("ryzen 5 3600")) return 50;

    return 10; // CPU básico ou não reconhecido
  };

  // Função para calcular pontos da RAM
  const getRamPoints = (ram) => {
    const ramLower = ram.toLowerCase();
    const ramSize = parseInt(ramLower.match(/\d+/)?.[0] || "0");

    if (ramSize >= 64) return 50;
    if (ramSize >= 32) return 40;
    if (ramSize >= 16) return 30;
    if (ramSize >= 8) return 20;
    if (ramSize >= 4) return 10;

    return 5; // Menos de 4GB
  };

  // Função para calcular pontos do Storage
  const getStoragePoints = (storage) => {
    const storageLower = storage.toLowerCase();

    if (storageLower.includes("nvme") || storageLower.includes("m.2")) {
      if (storageLower.includes("2tb") || storageLower.includes("2000"))
        return 30;
      if (storageLower.includes("1tb") || storageLower.includes("1000"))
        return 25;
      if (storageLower.includes("512") || storageLower.includes("500"))
        return 20;
      return 15; // NVMe menor
    }

    if (storageLower.includes("ssd")) {
      if (storageLower.includes("2tb") || storageLower.includes("2000"))
        return 25;
      if (storageLower.includes("1tb") || storageLower.includes("1000"))
        return 20;
      if (storageLower.includes("512") || storageLower.includes("500"))
        return 15;
      return 10; // SSD menor
    }

    if (storageLower.includes("hdd")) {
      return 5; // HDD
    }

    return 5; // Storage não especificado
  };

  // Função para obter jogos do usuário
  const getUserGames = async (userId) => {
    try {
      const games = await databaseService.getGames();
      return games.filter((game) => game.added_by_id === userId);
    } catch (error) {
      console.error("Erro ao obter jogos do usuário:", error);
      const games = JSON.parse(localStorage.getItem("gamesDatabase") || "[]");
      return games.filter((game) => game.added_by_id === userId);
    }
  };

  // Função para adicionar jogo ao usuário
  const addGameToUser = async (gameData) => {
    const newGame = {
      ...gameData,
      id: Date.now(),
      added_by_id: user.id,
      added_by: user.username,
      added_date: new Date().toISOString(),
      completed: false, // Por padrão, jogo não está completado
    };

    try {
      await databaseService.createGame(newGame);

      // Recalcular rank do usuário baseado nos jogos
      await updateUserRankFromGames();

      addLog("GAME_ADDED", `Jogo "${gameData.name}" adicionado à biblioteca`);
      return { success: true, game: newGame };
    } catch (error) {
      console.error("Erro ao adicionar jogo:", error);

      // Fallback para localStorage
      const games = JSON.parse(localStorage.getItem("gamesDatabase") || "[]");
      games.push(newGame);
      localStorage.setItem("gamesDatabase", JSON.stringify(games));

      await updateUserRankFromGames();
      addLog("GAME_ADDED", `Jogo "${gameData.name}" adicionado à biblioteca`);
      return { success: true, game: newGame };
    }
  };

  // Função para remover jogo do usuário
  const removeGameFromUser = async (gameId) => {
    try {
      // Buscar jogos do servidor
      const games = await databaseService.getGames();
      const gameToRemove = games.find(
        (g) => g.id == gameId && g.added_by_id === user.id
      );

      if (gameToRemove) {
        // Remover do servidor
        await databaseService.deleteGame(gameId);

        // Recalcular rank do usuário
        await updateUserRankFromGames();

        addLog(
          "GAME_REMOVED",
          `Jogo "${gameToRemove.name}" removido da biblioteca`
        );
        return { success: true };
      }

      return { success: false, message: "Jogo não encontrado" };
    } catch (error) {
      console.error("Erro ao remover jogo:", error);

      // Fallback para localStorage
      const games = JSON.parse(localStorage.getItem("gamesDatabase") || "[]");
      const gameToRemove = games.find(
        (g) => g.id == gameId && g.added_by_id === user.id
      );

      if (gameToRemove) {
        const updatedGames = games.filter((g) => g.id != gameId);
        localStorage.setItem("gamesDatabase", JSON.stringify(updatedGames));

        await updateUserRankFromGames();

        addLog(
          "GAME_REMOVED",
          `Jogo "${gameToRemove.name}" removido da biblioteca`
        );
        return { success: true };
      }

      return { success: false, message: "Jogo não encontrado" };
    }
  };

  // Função para atualizar jogo do usuário
  const updateUserGame = async (gameId, updatedData) => {
    try {
      // Buscar jogos do servidor
      const games = await databaseService.getGames();
      const gameIndex = games.findIndex(
        (g) => g.id == gameId && g.added_by_id === user.id
      );

      if (gameIndex !== -1) {
        const updatedGame = { ...games[gameIndex], ...updatedData };

        // Atualizar no servidor
        await databaseService.updateGame(gameId, updatedGame);

        addLog("GAME_UPDATED", `Jogo "${updatedGame.name}" atualizado`);
        return { success: true };
      }

      return { success: false, message: "Jogo não encontrado" };
    } catch (error) {
      console.error("Erro ao atualizar jogo:", error);

      // Fallback para localStorage
      const games = JSON.parse(localStorage.getItem("gamesDatabase") || "[]");
      const gameIndex = games.findIndex(
        (g) => g.id == gameId && g.added_by_id === user.id
      );

      if (gameIndex !== -1) {
        games[gameIndex] = { ...games[gameIndex], ...updatedData };
        localStorage.setItem("gamesDatabase", JSON.stringify(games));

        addLog("GAME_UPDATED", `Jogo "${games[gameIndex].name}" atualizado`);
        return { success: true };
      }

      return { success: false, message: "Jogo não encontrado" };
    }
  };

  // Função para marcar jogo como completado/não completado
  const toggleGameCompleted = async (gameId) => {
    try {
      // Buscar jogos do servidor
      const games = await databaseService.getGames();
      const gameIndex = games.findIndex(
        (g) => g.id == gameId && g.added_by_id === user.id
      );

      if (gameIndex !== -1) {
        const updatedGame = {
          ...games[gameIndex],
          completed: !games[gameIndex].completed,
        };

        // Atualizar no servidor
        await databaseService.updateGame(gameId, updatedGame);

        // Recalcular rank do usuário
        await updateUserRankFromGames();

        const status = updatedGame.completed ? "completado" : "não completado";
        addLog(
          "GAME_COMPLETED",
          `Jogo "${updatedGame.name}" marcado como ${status}`
        );

        return { success: true, completed: updatedGame.completed };
      }

      return { success: false, message: "Jogo não encontrado" };
    } catch (error) {
      console.error("Erro ao alterar status do jogo:", error);

      // Fallback para localStorage
      const games = JSON.parse(localStorage.getItem("gamesDatabase") || "[]");
      const gameIndex = games.findIndex(
        (g) => g.id == gameId && g.added_by_id === user.id
      );

      if (gameIndex !== -1) {
        games[gameIndex].completed = !games[gameIndex].completed;
        localStorage.setItem("gamesDatabase", JSON.stringify(games));

        await updateUserRankFromGames();

        const status = games[gameIndex].completed
          ? "completado"
          : "não completado";
        addLog(
          "GAME_COMPLETED",
          `Jogo "${games[gameIndex].name}" marcado como ${status}`
        );
        return { success: true, completed: games[gameIndex].completed };
      }

      return { success: false, message: "Jogo não encontrado" };
    }
  };

  // Função para calcular rank baseado nos jogos
  const updateUserRankFromGames = async () => {
    if (!user) return;

    const userGames = await getUserGames(user.id);
    const completedGames = userGames.filter((game) => game.completed === true);

    // Pontos apenas por jogos completados (1 ponto cada)
    const gamePoints = completedGames.length;

    // Combinar pontos do PC com pontos dos jogos completados
    const pcPoints = user.pcRank?.points || 0;
    const totalPoints = pcPoints + gamePoints;

    // Determinar novo rank
    let newRank;
    if (totalPoints >= 400) {
      newRank = { rank: "Lendário", emoji: "👑", points: totalPoints };
    } else if (totalPoints >= 300) {
      newRank = { rank: "Diamante", emoji: "💎", points: totalPoints };
    } else if (totalPoints >= 200) {
      newRank = { rank: "Ouro", emoji: "🥇", points: totalPoints };
    } else if (totalPoints >= 100) {
      newRank = { rank: "Prata", emoji: "🥈", points: totalPoints };
    } else if (totalPoints >= 50) {
      newRank = { rank: "Bronze", emoji: "🥉", points: totalPoints };
    } else {
      newRank = { rank: "Lixo", emoji: "💩", points: totalPoints };
    }

    // Atualizar rank do usuário
    updateProfile({ pcRank: newRank });
  };

  // Função para obter top players (baseado no total de jogos)
  const getTopPlayers = async () => {
    const users = await getDatabase();

    // Calcular ranking de cada usuário baseado no total de jogos
    const userRanking = await Promise.all(
      users.map(async (user) => {
        const userGames = await getUserGames(user.id);
        const completedGames = userGames.filter(
          (game) => game.completed === true
        );

        // Score baseado no total de jogos (não apenas completados)
        const gameScore = userGames.length;

        return {
          ...user,
          totalGames: userGames.length,
          completedGames: completedGames.length,
          gameScore: gameScore, // Score baseado no total de jogos
          activityScore: gameScore, // Manter compatibilidade
        };
      })
    );

    // Ordenar por total de jogos (decrescente) e retornar TODOS os usuários
    return userRanking.sort((a, b) => b.gameScore - a.gameScore);
  };

  // Função para obter top PC (baseado em PC Rank)
  const getTopPc = () => {
    const users = getDatabase();

    // Filtrar usuários com PC Rank e ordenar por pontos
    return users
      .filter((user) => user.pcRank && user.pcRank.points > 0)
      .sort((a, b) => b.pcRank.points - a.pcRank.points)
      .slice(0, 5);
  };

  const value = {
    user,
    login,
    register,
    logout,
    loading,
    getAllUsers,
    addLog,
    getLogs,
    updateProfile,
    calculatePcRank,
    getTopPlayers,
    getTopPc,
    getUserGames,
    addGameToUser,
    removeGameFromUser,
    updateUserGame,
    updateUserRankFromGames,
    toggleGameCompleted,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
