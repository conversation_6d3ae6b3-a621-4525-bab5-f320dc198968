import React from 'react';
import { Link } from 'react-router-dom';

const Welcome = () => {
  return (
    <div className="main-content">
      <div style={{
        textAlign: 'center',
        maxWidth: '800px',
        margin: '0 auto',
        padding: '2rem 0'
      }}>
        <h1 style={{
          fontSize: '3rem',
          marginBottom: '1rem',
          background: 'linear-gradient(45deg, #007bff, #28a745)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          backgroundClip: 'text'
        }}>
          🎮 GameTracker
        </h1>
        
        <p style={{
          fontSize: '1.2rem',
          color: '#6c757d',
          marginBottom: '2rem',
          lineHeight: '1.6'
        }}>
          O sistema completo para rastrear seus jogos, gerenciar sua biblioteca 
          e competir com outros gamers através do nosso sistema de PC Rank!
        </p>

        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
          gap: '2rem',
          marginBottom: '3rem'
        }}>
          <div style={{
            backgroundColor: '#f8f9fa',
            padding: '2rem',
            borderRadius: '12px',
            border: '1px solid #dee2e6'
          }}>
            <div style={{fontSize: '3rem', marginBottom: '1rem'}}>🎮</div>
            <h3 style={{color: '#495057', marginBottom: '1rem'}}>Biblioteca de Jogos</h3>
            <p style={{color: '#6c757d', fontSize: '0.9rem'}}>
              Adicione jogos da Steam ou manualmente. Organize sua coleção e 
              acompanhe seu progresso.
            </p>
          </div>

          <div style={{
            backgroundColor: '#f8f9fa',
            padding: '2rem',
            borderRadius: '12px',
            border: '1px solid #dee2e6'
          }}>
            <div style={{fontSize: '3rem', marginBottom: '1rem'}}>🏆</div>
            <h3 style={{color: '#495057', marginBottom: '1rem'}}>PC Rank System</h3>
            <p style={{color: '#6c757d', fontSize: '0.9rem'}}>
              Configure suas especificações de PC e receba um rank automático. 
              Compete com outros gamers!
            </p>
          </div>

          <div style={{
            backgroundColor: '#f8f9fa',
            padding: '2rem',
            borderRadius: '12px',
            border: '1px solid #dee2e6'
          }}>
            <div style={{fontSize: '3rem', marginBottom: '1rem'}}>👥</div>
            <h3 style={{color: '#495057', marginBottom: '1rem'}}>Rankings</h3>
            <p style={{color: '#6c757d', fontSize: '0.9rem'}}>
              Veja os top players mais ativos e os PCs mais poderosos da 
              comunidade.
            </p>
          </div>
        </div>

        <div style={{
          backgroundColor: '#007bff',
          color: 'white',
          padding: '2rem',
          borderRadius: '12px',
          marginBottom: '2rem'
        }}>
          <h2 style={{marginBottom: '1rem'}}>Pronto para começar?</h2>
          <p style={{marginBottom: '2rem', opacity: 0.9}}>
            Junte-se à comunidade GameTracker e comece a rastrear seus jogos hoje mesmo!
          </p>
          
          <div style={{
            display: 'flex',
            gap: '1rem',
            justifyContent: 'center',
            flexWrap: 'wrap'
          }}>
            <Link 
              to="/register" 
              style={{
                backgroundColor: 'white',
                color: '#007bff',
                padding: '1rem 2rem',
                borderRadius: '8px',
                textDecoration: 'none',
                fontWeight: 'bold',
                fontSize: '1.1rem'
              }}
            >
              📝 Criar Conta Grátis
            </Link>
            
            <Link 
              to="/login" 
              style={{
                backgroundColor: 'transparent',
                color: 'white',
                padding: '1rem 2rem',
                borderRadius: '8px',
                textDecoration: 'none',
                fontWeight: 'bold',
                fontSize: '1.1rem',
                border: '2px solid white'
              }}
            >
              🔑 Já tenho conta
            </Link>
          </div>
        </div>

        <div style={{
          backgroundColor: '#f8f9fa',
          padding: '1.5rem',
          borderRadius: '8px',
          border: '1px solid #dee2e6'
        }}>
          <h4 style={{color: '#495057', marginBottom: '0.5rem'}}>
            💡 Dica para Administradores
          </h4>
          <p style={{color: '#6c757d', fontSize: '0.9rem', margin: 0}}>
            Acesso admin: <strong>usuário: admin</strong> | <strong>senha: admin</strong>
          </p>
        </div>
      </div>
    </div>
  );
};

export default Welcome;
