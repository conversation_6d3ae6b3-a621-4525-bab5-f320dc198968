import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '../context/AuthContext';

export const useGames = () => {
  const { user, getUserGames } = useAuth();
  const [games, setGames] = useState([]);
  const [loading, setLoading] = useState(false);

  // Função para recarregar jogos
  const refreshGames = useCallback(async () => {
    if (!user) {
      setGames([]);
      return;
    }

    setLoading(true);
    try {
      const userGames = await getUserGames(user.id);
      setGames(userGames);
    } catch (error) {
      console.error('Erro ao carregar jogos:', error);
    } finally {
      setLoading(false);
    }
  }, [user, getUserGames]);

  // Carregar jogos quando usuário mudar
  useEffect(() => {
    refreshGames();
  }, [refreshGames]);

  return {
    games,
    loading,
    refreshGames
  };
};
