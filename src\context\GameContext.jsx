import React, { createContext, useContext, useState, useEffect } from "react";

const GameContext = createContext();

export const useGames = () => {
  const context = useContext(GameContext);
  if (!context) {
    throw new Error("useGames must be used within a GameProvider");
  }
  return context;
};

export const GameProvider = ({ children }) => {
  const [games, setGames] = useState([]);
  const [gameplays, setGameplays] = useState([]);
  const [loading, setLoading] = useState(true);
  const [pcConfig, setPcConfig] = useState({
    cpu: "",
    gpu: "",
    ram: "",
    storage: "",
    rank: {
      tier: "silver",
      name: "SILVER GAMER",
      emoji: "🥈",
    },
  });

  // Mock data
  const mockGames = [
    {
      id: 1,
      name: "Cyberpunk 2077",
      publisher: "CD Projekt Red",
      price: "$59.99",
      image_url:
        "https://images.unsplash.com/photo-**********-adc38448a05e?w=400&h=300&fit=crop",
      genre: "RPG",
      release_date: "2020-12-10",
    },
    {
      id: 2,
      name: "The Witcher 3",
      publisher: "CD Projekt Red",
      price: "$39.99",
      image_url:
        "https://images.unsplash.com/photo-1511512578047-dfb367046420?w=400&h=300&fit=crop",
      genre: "RPG",
      release_date: "2015-05-19",
    },
    {
      id: 3,
      name: "Red Dead Redemption 2",
      publisher: "Rockstar Games",
      price: "$49.99",
      image_url:
        "https://images.unsplash.com/photo-1493711662062-fa541adb3fc8?w=400&h=300&fit=crop",
      genre: "Action",
      release_date: "2018-10-26",
    },
    {
      id: 4,
      name: "Grand Theft Auto V",
      publisher: "Rockstar Games",
      price: "$29.99",
      image_url:
        "https://images.unsplash.com/photo-1552820728-8b83bb6b773f?w=400&h=300&fit=crop",
      genre: "Action",
      release_date: "2013-09-17",
    },
  ];

  const mockGameplays = [
    {
      id: 1,
      game_id: 1,
      user_id: 1,
      start_time: "2024-06-24T10:00:00",
      end_time: "2024-06-24T14:30:00",
      duration: 4.5,
    },
    {
      id: 2,
      game_id: 2,
      user_id: 1,
      start_time: "2024-06-23T15:00:00",
      end_time: "2024-06-23T18:00:00",
      duration: 3,
    },
  ];

  const mockPlayers = [
    {
      id: 1,
      username: "admin",
      completed_games: 15,
      total_hours: 127.5,
    },
    {
      id: 2,
      username: "rodrigo",
      completed_games: 8,
      total_hours: 89.2,
    },
    {
      id: 3,
      username: "player3",
      completed_games: 12,
      total_hours: 156.8,
    },
    {
      id: 4,
      username: "gamer_pro",
      completed_games: 6,
      total_hours: 78.3,
    },
    {
      id: 5,
      username: "casual_player",
      completed_games: 3,
      total_hours: 45.1,
    },
  ];

  useEffect(() => {
    // Simulate API loading
    setTimeout(() => {
      setGames(mockGames);
      setGameplays(mockGameplays);
      setLoading(false);
    }, 1000);
  }, []);

  const addGame = (gameData) => {
    const newGame = {
      id: Date.now(),
      ...gameData,
    };
    setGames((prev) => [...prev, newGame]);
    return newGame;
  };

  const logGameplay = (gameplayData) => {
    const newGameplay = {
      id: Date.now(),
      ...gameplayData,
      user_id: 1, // Mock user ID
    };
    setGameplays((prev) => [...prev, newGameplay]);
    return newGameplay;
  };

  const deleteGameplay = (gameplayId) => {
    setGameplays((prev) => prev.filter((g) => g.id !== gameplayId));
  };

  const getTopPlayers = () => {
    return mockPlayers.sort((a, b) => b.completed_games - a.completed_games);
  };

  const getStats = () => {
    return {
      total_players: mockPlayers.length,
      total_hours: mockPlayers.reduce(
        (sum, player) => sum + player.total_hours,
        0
      ),
      total_games: games.length,
      completed_gameplays: gameplays.length,
    };
  };

  // Função para calcular o rank baseado nas especificações
  const calculatePCRank = (cpu, gpu, ram, storage) => {
    let score = 0;

    // Pontuação CPU
    const cpuScores = {
      "intel-i9-13900k": 100,
      "amd-ryzen-9-7950x": 100,
      "intel-i7-13700k": 80,
      "amd-ryzen-7-7700x": 80,
      "intel-i5-13600k": 60,
      "amd-ryzen-5-7600x": 60,
      outros: 40,
    };

    // Pontuação GPU
    const gpuScores = {
      "rtx-4090": 100,
      "rtx-4080": 95,
      "rx-7900-xtx": 90,
      "rtx-4070-ti": 80,
      "rtx-4070": 75,
      "rx-7800-xt": 70,
      "rtx-3080": 65,
      "rtx-3070": 60,
      "outros-gpu": 40,
    };

    // Pontuação RAM
    const ramScores = {
      "64gb": 100,
      "32gb-ddr5": 90,
      "32gb-ddr4": 85,
      "16gb-ddr5": 70,
      "16gb-ddr4": 65,
      "8gb": 40,
      "menos-8gb": 20,
    };

    // Pontuação Storage
    const storageScores = {
      "nvme-gen4": 100,
      "nvme-gen3": 80,
      "sata-ssd": 60,
      hdd: 30,
      "hdd-antigo": 10,
    };

    score += cpuScores[cpu] || 0;
    score += gpuScores[gpu] || 0;
    score += ramScores[ram] || 0;
    score += storageScores[storage] || 0;

    // Calcular rank baseado na pontuação total
    const avgScore = score / 4;

    if (avgScore >= 90) {
      return { tier: "diamond", name: "DIAMOND GAMER", emoji: "💎" };
    } else if (avgScore >= 70) {
      return { tier: "gold", name: "GOLD GAMER", emoji: "🥇" };
    } else if (avgScore >= 50) {
      return { tier: "silver", name: "SILVER GAMER", emoji: "🥈" };
    } else if (avgScore >= 30) {
      return { tier: "bronze", name: "BRONZE GAMER", emoji: "🥉" };
    } else {
      return { tier: "potato", name: "POTATO GAMER", emoji: "💩" };
    }
  };

  // Função para salvar configuração do PC
  const savePCConfig = (cpu, gpu, ram, storage) => {
    const rank = calculatePCRank(cpu, gpu, ram, storage);
    const newConfig = {
      cpu,
      gpu,
      ram,
      storage,
      rank,
    };

    setPcConfig(newConfig);
    localStorage.setItem("pcConfig", JSON.stringify(newConfig));
    return rank;
  };

  // Carregar configuração do localStorage
  useEffect(() => {
    const savedConfig = localStorage.getItem("pcConfig");
    if (savedConfig) {
      setPcConfig(JSON.parse(savedConfig));
    }
  }, []);

  const value = {
    games,
    gameplays,
    loading,
    pcConfig,
    addGame,
    logGameplay,
    deleteGameplay,
    getTopPlayers,
    getStats,
    savePCConfig,
    calculatePCRank,
  };

  return <GameContext.Provider value={value}>{children}</GameContext.Provider>;
};
