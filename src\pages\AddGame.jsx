import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { useLocation, useNavigate } from "react-router-dom";
import {
  Cloud,
  Gamepad2,
  Search,
  Plus,
  Star,
  Calendar,
  Users,
} from "lucide-react";
import { useGames } from "../context/GameContext";

const AddGame = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { addGame } = useGames();
  const isManual = location.pathname === "/add-game-manual";

  const [searchTerm, setSearchTerm] = useState("");
  const [searchResults, setSearchResults] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedGame, setSelectedGame] = useState(null);

  // Configuração da API SteamDB
  const STEAMDB_TOKEN =
    "eyAidHlwIjogIkpXVCIsICJhbGciOiAiRWREU0EiIH0.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.lhtrraYLhxrvTejRseSh4_fkOeru1fAYyhC4FqHSULvrVguvpXgmUGy4wS8uBBfzC27TRwIs8IlZGWy3el8TBw";
  const API_KEY = "E18E6653E82312D3893D67AE26427C37";

  // Função para buscar jogos na Steam API usando SteamDB
  const searchSteamGames = async (query) => {
    if (!query.trim()) return;

    setLoading(true);
    try {
      // Tentativa de usar a API real do SteamDB
      const response = await fetch(
        `https://steamdb.info/api/search/?q=${encodeURIComponent(query)}`,
        {
          headers: {
            Authorization: `Bearer ${STEAMDB_TOKEN}`,
            "X-API-Key": API_KEY,
            "Content-Type": "application/json",
          },
        }
      );

      let results = [];

      if (response.ok) {
        const data = await response.json();
        results = data.results || [];
      } else {
        // Fallback para dados mock se a API falhar
        console.log("API SteamDB não disponível, usando dados mock");
        const mockResults = [
          {
            appid: 1091500,
            name: "Cyberpunk 2077",
            developer: "CD PROJEKT RED",
            publisher: "CD PROJEKT RED",
            release_date: "2020-12-10",
            price: "R$ 199,90",
            image:
              "https://cdn.akamai.steamstatic.com/steam/apps/1091500/header.jpg",
            description:
              "Cyberpunk 2077 é um RPG de ação e aventura em mundo aberto que se passa em Night City.",
            tags: ["RPG", "Mundo Aberto", "Cyberpunk", "Ação"],
            rating: 4.2,
          },
          {
            appid: 292030,
            name: "The Witcher 3: Wild Hunt",
            developer: "CD PROJEKT RED",
            publisher: "CD PROJEKT RED",
            release_date: "2015-05-19",
            price: "R$ 39,99",
            image:
              "https://cdn.akamai.steamstatic.com/steam/apps/292030/header.jpg",
            description:
              "The Witcher 3: Wild Hunt é um RPG de mundo aberto de nova geração.",
            tags: ["RPG", "Mundo Aberto", "Fantasia", "História Rica"],
            rating: 4.8,
          },
          {
            appid: 1174180,
            name: "Red Dead Redemption 2",
            developer: "Rockstar Games",
            publisher: "Rockstar Games",
            release_date: "2019-12-05",
            price: "R$ 299,90",
            image:
              "https://cdn.akamai.steamstatic.com/steam/apps/1174180/header.jpg",
            description:
              "Red Dead Redemption 2 é um épico de ação e aventura ambientado no coração da América.",
            tags: ["Ação", "Aventura", "Mundo Aberto", "Western"],
            rating: 4.6,
          },
          {
            appid: 271590,
            name: "Grand Theft Auto V",
            developer: "Rockstar North",
            publisher: "Rockstar Games",
            release_date: "2015-04-14",
            price: "R$ 89,90",
            image:
              "https://cdn.akamai.steamstatic.com/steam/apps/271590/header.jpg",
            description:
              "Grand Theft Auto V para PC oferece aos jogadores a opção de explorar o mundo premiado e enorme de Los Santos e Blaine County.",
            tags: ["Ação", "Aventura", "Mundo Aberto", "Crime"],
            rating: 4.4,
          },
          {
            appid: 1245620,
            name: "ELDEN RING",
            developer: "FromSoftware Inc.",
            publisher: "Bandai Namco Entertainment",
            release_date: "2022-02-25",
            price: "R$ 249,90",
            image:
              "https://cdn.akamai.steamstatic.com/steam/apps/1245620/header.jpg",
            description:
              "O NOVO RPG DE AÇÃO E FANTASIA. Levante-se, Maculado, e seja guiado pela graça para portar o poder do Anel Prístino e se tornar um Lorde Prístino nas Terras Intermédias.",
            tags: ["RPG", "Souls-like", "Fantasia Sombria", "Mundo Aberto"],
            rating: 4.7,
          },
          {
            appid: 1174180,
            name: "Minecraft",
            developer: "Mojang Studios",
            publisher: "Microsoft Studios",
            release_date: "2011-11-18",
            price: "R$ 26,95",
            image:
              "https://cdn.akamai.steamstatic.com/steam/apps/1174180/header.jpg",
            description:
              "Minecraft é um jogo sobre colocar blocos e ir em aventuras. Explore mundos gerados aleatoriamente e construa coisas incríveis.",
            tags: ["Sandbox", "Sobrevivência", "Construção", "Indie"],
            rating: 4.5,
          },
        ];

        // Filtrar resultados baseado na busca
        results = mockResults.filter(
          (game) =>
            game.name.toLowerCase().includes(query.toLowerCase()) ||
            game.developer.toLowerCase().includes(query.toLowerCase()) ||
            game.tags.some((tag) =>
              tag.toLowerCase().includes(query.toLowerCase())
            )
        );
      }

      setSearchResults(results);
    } catch (error) {
      console.error("Erro ao buscar jogos:", error);

      // Fallback para dados mock em caso de erro
      const mockResults = [
        {
          appid: 1091500,
          name: "Cyberpunk 2077",
          developer: "CD PROJEKT RED",
          publisher: "CD PROJEKT RED",
          release_date: "2020-12-10",
          price: "R$ 199,90",
          image:
            "https://cdn.akamai.steamstatic.com/steam/apps/1091500/header.jpg",
          description:
            "Cyberpunk 2077 é um RPG de ação e aventura em mundo aberto que se passa em Night City.",
          tags: ["RPG", "Mundo Aberto", "Cyberpunk", "Ação"],
          rating: 4.2,
        },
        {
          appid: 292030,
          name: "The Witcher 3: Wild Hunt",
          developer: "CD PROJEKT RED",
          publisher: "CD PROJEKT RED",
          release_date: "2015-05-19",
          price: "R$ 39,99",
          image:
            "https://cdn.akamai.steamstatic.com/steam/apps/292030/header.jpg",
          description:
            "The Witcher 3: Wild Hunt é um RPG de mundo aberto de nova geração.",
          tags: ["RPG", "Mundo Aberto", "Fantasia", "História Rica"],
          rating: 4.8,
        },
      ];

      const filtered = mockResults.filter(
        (game) =>
          game.name.toLowerCase().includes(query.toLowerCase()) ||
          game.developer.toLowerCase().includes(query.toLowerCase())
      );

      setSearchResults(filtered);
    } finally {
      setLoading(false);
    }
  };

  // Função para adicionar jogo à biblioteca
  const handleAddGame = async (game) => {
    try {
      const gameData = {
        id: Date.now(),
        name: game.name,
        publisher: game.publisher,
        developer: game.developer,
        release_date: game.release_date,
        image_url: game.image,
        description: game.description,
        tags: game.tags,
        rating: game.rating,
        steam_appid: game.appid,
        price: game.price,
        added_date: new Date().toISOString(),
      };

      addGame(gameData);
      setSelectedGame(game);

      // Redirecionar para a biblioteca após 2 segundos
      setTimeout(() => {
        navigate("/library");
      }, 2000);
    } catch (error) {
      console.error("Erro ao adicionar jogo:", error);
    }
  };

  // Buscar automaticamente quando o usuário digita
  useEffect(() => {
    const delayedSearch = setTimeout(() => {
      if (searchTerm) {
        searchSteamGames(searchTerm);
      } else {
        setSearchResults([]);
      }
    }, 500);

    return () => clearTimeout(delayedSearch);
  }, [searchTerm]);

  if (selectedGame) {
    return (
      <div className="min-h-screen p-6 flex items-center justify-center">
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          className="glass-card max-w-md mx-auto text-center"
        >
          <div className="text-6xl mb-4">🎉</div>
          <h2 className="text-2xl font-bold text-white mb-2">
            Jogo Adicionado!
          </h2>
          <p className="text-gray-300 mb-4">
            {selectedGame.name} foi adicionado à sua biblioteca
          </p>
          <div className="text-sm text-gray-400">
            Redirecionando para a biblioteca...
          </div>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="min-h-screen p-6">
      <div className="max-w-6xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          {/* Header */}
          <div className="text-center mb-8">
            {isManual ? (
              <>
                <Gamepad2 className="h-16 w-16 text-purple-400 mx-auto mb-4" />
                <h1 className="text-4xl font-bold gradient-text mb-4">
                  Add Game Manual
                </h1>
                <p className="text-xl text-gray-300">
                  Adicione jogos manualmente ao seu tracker
                </p>
              </>
            ) : (
              <>
                <div className="flex items-center justify-center mb-4">
                  <Cloud className="h-16 w-16 text-blue-400" />
                </div>
                <h1 className="text-4xl font-bold gradient-text mb-4">
                  Buscar Jogos Steam
                </h1>
                <p className="text-xl text-gray-300">
                  Busque e adicione jogos da Steam à sua biblioteca
                </p>
              </>
            )}
          </div>

          {/* Search Bar */}
          <div className="max-w-2xl mx-auto mb-8">
            <div className="relative">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <input
                type="text"
                placeholder="Buscar jogos na Steam..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-12 pr-4 py-4 bg-gray-800/50 border border-gray-700 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20"
              />
            </div>
          </div>

          {/* Loading */}
          {loading && (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
              <p className="text-gray-400">Buscando jogos...</p>
            </div>
          )}

          {/* Search Results */}
          {searchResults.length > 0 && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {searchResults.map((game) => (
                <motion.div
                  key={game.appid}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="glass-card group hover:scale-105 transition-all duration-300"
                >
                  <div className="relative">
                    <img
                      src={game.image}
                      alt={game.name}
                      className="w-full h-48 object-cover rounded-t-xl"
                    />
                    <div className="absolute top-4 right-4 bg-black/70 px-2 py-1 rounded-lg">
                      <div className="flex items-center text-yellow-400">
                        <Star className="h-4 w-4 mr-1 fill-current" />
                        <span className="text-sm">{game.rating}</span>
                      </div>
                    </div>
                  </div>

                  <div className="p-6">
                    <h3 className="text-xl font-bold text-white mb-2">
                      {game.name}
                    </h3>
                    <p className="text-gray-400 text-sm mb-2">
                      {game.developer}
                    </p>
                    <p className="text-gray-300 text-sm mb-4 line-clamp-2">
                      {game.description}
                    </p>

                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center text-gray-400 text-sm">
                        <Calendar className="h-4 w-4 mr-1" />
                        <span>{new Date(game.release_date).getFullYear()}</span>
                      </div>
                      <div className="text-green-400 font-semibold">
                        {game.price}
                      </div>
                    </div>

                    <div className="flex flex-wrap gap-2 mb-4">
                      {game.tags.slice(0, 3).map((tag, index) => (
                        <span
                          key={index}
                          className="px-2 py-1 bg-blue-600/20 text-blue-300 rounded-full text-xs"
                        >
                          {tag}
                        </span>
                      ))}
                    </div>

                    <button
                      onClick={() => handleAddGame(game)}
                      className="w-full btn-primary flex items-center justify-center"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Adicionar à Biblioteca
                    </button>
                  </div>
                </motion.div>
              ))}
            </div>
          )}

          {/* Empty State */}
          {!loading && searchTerm && searchResults.length === 0 && (
            <div className="text-center py-12">
              <Gamepad2 className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-white mb-2">
                Nenhum jogo encontrado
              </h3>
              <p className="text-gray-400">Tente buscar por outro termo</p>
            </div>
          )}

          {/* Initial State */}
          {!searchTerm && (
            <div className="text-center py-12">
              <Search className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-white mb-2">
                Busque por jogos
              </h3>
              <p className="text-gray-400">
                Digite o nome de um jogo para começar a busca
              </p>
            </div>
          )}
        </motion.div>
      </div>
    </div>
  );
};

export default AddGame;
