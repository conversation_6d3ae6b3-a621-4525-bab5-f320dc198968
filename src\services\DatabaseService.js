// Serviço de banco de dados simulado para funcionar como servidor
class DatabaseService {
  constructor() {
    // Detectar automaticamente o host correto
    const hostname = window.location.hostname;
    const serverHost = hostname === "localhost" ? "localhost" : hostname;
    this.baseUrl = `http://${serverHost}:3001/api`; // URL do backend
    this.fallbackToLocal = true; // Fallback para localStorage se servidor não estiver disponível

    console.log(`🔗 DatabaseService conectando em: ${this.baseUrl}`);
  }

  // Método genérico para fazer requisições
  async makeRequest(endpoint, method = "GET", data = null) {
    try {
      const options = {
        method,
        headers: {
          "Content-Type": "application/json",
        },
      };

      if (data) {
        options.body = JSON.stringify(data);
      }

      const response = await fetch(`${this.baseUrl}${endpoint}`, options);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.warn(
        "Servidor não disponível, usando localStorage:",
        error.message
      );

      if (this.fallbackToLocal) {
        return this.handleLocalFallback(endpoint, method, data);
      }

      throw error;
    }
  }

  // Fallback para localStorage quando servidor não está disponível
  handleLocalFallback(endpoint, method, data) {
    const key = endpoint.replace("/", "_");

    switch (method) {
      case "GET":
        return JSON.parse(localStorage.getItem(key) || "[]");

      case "POST":
        const existing = JSON.parse(localStorage.getItem(key) || "[]");
        existing.push(data);
        localStorage.setItem(key, JSON.stringify(existing));
        return data;

      case "PUT":
        localStorage.setItem(key, JSON.stringify(data));
        return data;

      case "DELETE":
        localStorage.removeItem(key);
        return { success: true };

      default:
        return null;
    }
  }

  // Métodos para usuários
  async getUsers() {
    return await this.makeRequest("/users");
  }

  async createUser(userData) {
    return await this.makeRequest("/users", "POST", userData);
  }

  async updateUser(userId, userData) {
    return await this.makeRequest(`/users/${userId}`, "PUT", userData);
  }

  async deleteUser(userId) {
    return await this.makeRequest(`/users/${userId}`, "DELETE");
  }

  // Métodos para jogos
  async getGames() {
    return await this.makeRequest("/games");
  }

  async createGame(gameData) {
    return await this.makeRequest("/games", "POST", gameData);
  }

  async updateGame(gameId, gameData) {
    return await this.makeRequest(`/games/${gameId}`, "PUT", gameData);
  }

  async deleteGame(gameId) {
    return await this.makeRequest(`/games/${gameId}`, "DELETE");
  }

  // Métodos para logs
  async getLogs() {
    return await this.makeRequest("/logs");
  }

  async createLog(logData) {
    return await this.makeRequest("/logs", "POST", logData);
  }

  // Método para sincronizar dados locais com o servidor
  async syncLocalData() {
    try {
      // Tentar enviar dados locais para o servidor
      const localUsers = JSON.parse(localStorage.getItem("users") || "[]");
      const localGames = JSON.parse(
        localStorage.getItem("gamesDatabase") || "[]"
      );
      const localLogs = JSON.parse(localStorage.getItem("logs") || "[]");

      if (localUsers.length > 0) {
        await this.makeRequest("/sync/users", "POST", localUsers);
      }

      if (localGames.length > 0) {
        await this.makeRequest("/sync/games", "POST", localGames);
      }

      if (localLogs.length > 0) {
        await this.makeRequest("/sync/logs", "POST", localLogs);
      }

      console.log("Dados sincronizados com sucesso!");
    } catch (error) {
      console.warn("Falha na sincronização:", error.message);
    }
  }

  // Método para verificar se o servidor está online
  async isServerOnline() {
    try {
      const response = await fetch(`${this.baseUrl}/health`);
      return response.ok;
    } catch {
      return false;
    }
  }
}

// Instância singleton
const databaseService = new DatabaseService();

export default databaseService;
