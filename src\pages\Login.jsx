import React, { useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import { useAuth } from "../context/AuthContext";

const Login = () => {
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [error, setError] = useState("");
  const { login } = useAuth();
  const navigate = useNavigate();

  const handleSubmit = (e) => {
    e.preventDefault();
    setError("");

    if (!username || !password) {
      setError("Por favor, preencha todos os campos");
      return;
    }

    const result = login(username, password);
    if (result.success) {
      navigate("/profile");
    } else {
      setError(result.message || "Usuário ou senha inválidos");
    }
  };

  return (
    <div className="main-content">
      <h1>Login</h1>
      <form onSubmit={handleSubmit}>
        <div>
          <label>Usu<PERSON><PERSON>:</label>
          <input
            type="text"
            value={username}
            onChange={(e) => setUsername(e.target.value)}
            placeholder="Digite seu nome de usuário"
          />
        </div>

        <div>
          <label>Senha:</label>
          <input
            type="password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            placeholder="Digite sua senha"
          />
        </div>

        {error && <div style={{ color: "red" }}>{error}</div>}

        <button type="submit">Entrar</button>
      </form>

      <p>
        Não tem uma conta? <Link to="/register">Registre-se aqui</Link>
      </p>
    </div>
  );
};

export default Login;
