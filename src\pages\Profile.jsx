import React from "react";
import { useAuth } from "../context/AuthContext";
import { useNavigate } from "react-router-dom";

const Profile = () => {
  const { user, logout } = useAuth();
  const navigate = useNavigate();

  const handleLogout = () => {
    logout();
    navigate("/login");
  };

  if (!user) {
    return <div>Carregando...</div>;
  }

  return (
    <div className="main-content">
      <h1>Perfil do Usuário</h1>

      <div>
        <img
          src={user.avatar}
          alt="Avatar"
          style={{ width: "100px", height: "100px", borderRadius: "50%" }}
        />
      </div>

      <div>
        <h2>Informações Pessoais</h2>
        <p>
          <strong>Nome:</strong> {user.name}
        </p>
        <p>
          <strong>Usuário:</strong> {user.username}
        </p>
        <p>
          <strong>ID:</strong> {user.id}
        </p>
        <p>
          <strong>Membro desde:</strong>{" "}
          {new Date(user.createdAt).toLocaleDateString("pt-BR")}
        </p>
      </div>

      <div>
        <h2>Ações</h2>
        <button onClick={handleLogout}>Sair</button>
      </div>
    </div>
  );
};

export default Profile;
