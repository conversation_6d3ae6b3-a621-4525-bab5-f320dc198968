import React from "react";
import { useAuth } from "../context/AuthContext";
import { useNavigate, Link } from "react-router-dom";

const Profile = () => {
  const { user, logout } = useAuth();
  const navigate = useNavigate();

  const handleLogout = () => {
    logout();
    navigate("/login");
  };

  if (!user) {
    return <div>Carregando...</div>;
  }

  return (
    <div className="main-content">
      <h1>Perfil do Usuário</h1>

      {/* Banner */}
      {user.banner && (
        <div
          style={{
            marginBottom: "20px",
            border: "2px solid #dee2e6",
            borderRadius: "8px",
            overflow: "hidden",
            height: "200px",
            width: "100%",
            maxWidth: "600px",
          }}
        >
          {user.banner.includes(".mp4") || user.banner.includes(".webm") ? (
            <video
              src={user.banner}
              style={{ width: "100%", height: "100%", objectFit: "cover" }}
              autoPlay
              muted
              loop
            />
          ) : (
            <img
              src={user.banner}
              alt="Banner do usuário"
              style={{ width: "100%", height: "100%", objectFit: "cover" }}
            />
          )}
        </div>
      )}

      <div>
        <img
          src={user.avatar}
          alt="Avatar"
          style={{ width: "100px", height: "100px", borderRadius: "50%" }}
        />
      </div>

      <div>
        <h2>Informações Pessoais</h2>
        <p>
          <strong>Nome:</strong> {user.name}
        </p>
        <p>
          <strong>Usuário:</strong> {user.username}
        </p>
        <p>
          <strong>ID:</strong> {user.id}
        </p>
        <p>
          <strong>Membro desde:</strong>{" "}
          {new Date(user.createdAt).toLocaleDateString("pt-BR")}
        </p>
        {user.favoriteCompany && (
          <p>
            <strong>Empresa Favorita:</strong> {user.favoriteCompany}
          </p>
        )}
        {user.pcRank && (
          <p>
            <strong>PC Rank:</strong> {user.pcRank.emoji} {user.pcRank.rank} (
            {user.pcRank.points} pontos)
          </p>
        )}
      </div>

      {/* Especificações do PC */}
      {user.pcSpecs &&
        (user.pcSpecs.gpu ||
          user.pcSpecs.cpu ||
          user.pcSpecs.ram ||
          user.pcSpecs.storage) && (
          <div>
            <h2>Especificações do PC</h2>
            {user.pcSpecs.gpu && (
              <p>
                <strong>Placa de Vídeo:</strong> {user.pcSpecs.gpu}
              </p>
            )}
            {user.pcSpecs.cpu && (
              <p>
                <strong>Processador:</strong> {user.pcSpecs.cpu}
              </p>
            )}
            {user.pcSpecs.ram && (
              <p>
                <strong>Memória RAM:</strong> {user.pcSpecs.ram}
              </p>
            )}
            {user.pcSpecs.storage && (
              <p>
                <strong>Armazenamento:</strong> {user.pcSpecs.storage}
              </p>
            )}
          </div>
        )}

      <div>
        <h2>Ações</h2>
        <div style={{ display: "flex", gap: "10px", flexWrap: "wrap" }}>
          <Link to="/edit-profile" className="btn btn-primary">
            ✏️ Editar Perfil
          </Link>
          <button onClick={handleLogout} className="btn btn-danger">
            🚪 Sair
          </button>
        </div>
      </div>
    </div>
  );
};

export default Profile;
