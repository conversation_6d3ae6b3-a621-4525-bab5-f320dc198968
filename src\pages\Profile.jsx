import React, { useState } from "react";
import { motion } from "framer-motion";
import {
  ArrowLeft,
  Calendar,
  MapPin,
  Link as LinkIcon,
  MoreHorizontal,
  Trophy,
  Clock,
  Gamepad2,
  Target,
  Star,
  Play,
} from "lucide-react";
import { useAuth } from "../context/AuthContext";
import { useGames } from "../context/GameContext";

const Profile = () => {
  const [activeTab, setActiveTab] = useState("empresa-favorita");
  const { user } = useAuth();
  const { games, gameplays, getStats, pcConfig, savePCConfig } = useGames();

  // Estados para o formulário de PC Config
  const [formData, setFormData] = useState({
    cpu: pcConfig.cpu || "",
    gpu: pcConfig.gpu || "",
    ram: pcConfig.ram || "",
    storage: pcConfig.storage || "",
  });
  const [showSuccess, setShowSuccess] = useState(false);

  const stats = getStats();

  // Função para lidar com mudanças no formulário
  const handleInputChange = (field, value) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  // Função para salvar configuração
  const handleSaveConfig = () => {
    const { cpu, gpu, ram, storage } = formData;
    if (cpu && gpu && ram && storage) {
      savePCConfig(cpu, gpu, ram, storage);
      setShowSuccess(true);
      setTimeout(() => setShowSuccess(false), 3000);
    } else {
      alert("Por favor, preencha todas as configurações!");
    }
  };

  // Atualizar formData quando pcConfig mudar
  React.useEffect(() => {
    setFormData({
      cpu: pcConfig.cpu || "",
      gpu: pcConfig.gpu || "",
      ram: pcConfig.ram || "",
      storage: pcConfig.storage || "",
    });
  }, [pcConfig]);

  const tabs = [
    { id: "empresa-favorita", label: "Empresa Favorita", count: 5 },
    { id: "games", label: "Games", count: games.length },
    { id: "config-pc-rank", label: "Config PC Rank", count: 1 },
    { id: "highlights", label: "Highlights", count: 8 },
    { id: "media", label: "Media", count: 23 },
  ];

  const empresasFavoritas = [
    {
      id: 1,
      name: "CD Projekt RED",
      logo: "https://images.unsplash.com/photo-1542751371-adc38448a05e?w=60&h=60&fit=crop",
      description: "Criadores de The Witcher e Cyberpunk 2077",
      games: ["The Witcher 3", "Cyberpunk 2077"],
      founded: "1994",
      country: "Poland",
    },
    {
      id: 2,
      name: "Rockstar Games",
      logo: "https://images.unsplash.com/photo-1493711662062-fa541adb3fc8?w=60&h=60&fit=crop",
      description: "Mestres em mundos abertos e narrativas épicas",
      games: ["GTA V", "Red Dead Redemption 2"],
      founded: "1998",
      country: "USA",
    },
    {
      id: 3,
      name: "Naughty Dog",
      logo: "https://images.unsplash.com/photo-1511512578047-dfb367046420?w=60&h=60&fit=crop",
      description: "Especialistas em aventuras cinematográficas",
      games: ["The Last of Us", "Uncharted"],
      founded: "1984",
      country: "USA",
    },
  ];

  return (
    <div className="min-h-screen">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="flex items-center p-4 glass backdrop-blur-md sticky top-0 z-40">
          <ArrowLeft className="h-5 w-5 text-white mr-4 cursor-pointer hover:bg-white/10 rounded-full p-1 w-8 h-8" />
          <div>
            <h1 className="text-xl font-bold text-white">
              {user?.username || "User"}
            </h1>
            <p className="text-sm text-gray-400">Gaming Profile</p>
          </div>
        </div>

        {/* Banner */}
        <div className="relative">
          <div
            className="h-48 bg-gradient-to-r from-purple-600 via-blue-600 to-cyan-500"
            style={{
              backgroundImage:
                "url(https://images.unsplash.com/photo-1542751371-adc38448a05e?w=800&h=200&fit=crop)",
              backgroundSize: "cover",
              backgroundPosition: "center",
            }}
          />

          {/* Profile Picture */}
          <div className="absolute -bottom-16 left-4">
            <div className="w-32 h-32 rounded-full border-4 border-gray-900 overflow-hidden bg-gray-800">
              <img
                src={
                  user?.avatar ||
                  "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face"
                }
                alt="Profile"
                className="w-full h-full object-cover"
              />
            </div>
          </div>

          {/* Edit Profile Button */}
          <div className="absolute bottom-4 right-4">
            <button className="btn-secondary">Edit Profile</button>
          </div>
        </div>

        {/* Profile Info */}
        <div className="px-4 pt-20 pb-4">
          <div className="flex justify-between items-start mb-4">
            <div>
              <h2 className="text-2xl font-bold text-white">
                {user?.username || "Gaming Pro"}
              </h2>
              <p className="text-gray-400">
                @{user?.username?.toLowerCase() || "gamingpro"}
              </p>
            </div>
            <MoreHorizontal className="h-5 w-5 text-gray-400 cursor-pointer hover:bg-white/10 rounded-full p-1 w-8 h-8" />
          </div>

          <p className="text-white mb-4">
            🎮 Passionate gamer | 🏆 Achievement hunter | 🎯 Currently playing:
            Cyberpunk 2077
          </p>

          <div className="flex items-center space-x-4 text-gray-400 text-sm mb-4">
            <div className="flex items-center">
              <MapPin className="h-4 w-4 mr-1" />
              <span>São Paulo, Brazil</span>
            </div>
            <div className="flex items-center">
              <LinkIcon className="h-4 w-4 mr-1" />
              <span className="text-blue-400">gametracker.com</span>
            </div>
            <div className="flex items-center">
              <Calendar className="h-4 w-4 mr-1" />
              <span>Joined December 2023</span>
            </div>
          </div>

          {/* Stats */}
          <div className="flex space-x-6 mb-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-white">
                {stats.total_games}
              </div>
              <div className="text-sm text-gray-400">Games</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-white">
                {Math.round(stats.total_hours)}
              </div>
              <div className="text-sm text-gray-400">Hours</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-white">156</div>
              <div className="text-sm text-gray-400">Following</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-white">1.2K</div>
              <div className="text-sm text-gray-400">Followers</div>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-700">
          <div className="flex">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex-1 py-4 px-4 text-center font-medium transition-all duration-200 ${
                  activeTab === tab.id
                    ? "text-white border-b-2 border-blue-500"
                    : "text-gray-400 hover:text-white hover:bg-white/5"
                }`}
              >
                <span className="text-sm">{tab.label}</span>
                {tab.count > 0 && (
                  <span className="ml-1 text-xs text-gray-500">
                    ({tab.count})
                  </span>
                )}
              </button>
            ))}
          </div>
        </div>

        {/* Tab Content */}
        <div className="p-4">
          {(activeTab === "empresa-favorita" ||
            activeTab === "empresa-favorita-2") && (
            <div className="space-y-4">
              {empresasFavoritas.map((empresa) => (
                <motion.div
                  key={empresa.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="glass-card"
                >
                  <div className="flex space-x-4">
                    <img
                      src={empresa.logo}
                      alt={empresa.name}
                      className="w-16 h-16 rounded-lg object-cover"
                    />
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-2">
                        <h3 className="text-xl font-bold text-white">
                          {empresa.name}
                        </h3>
                        <span className="text-sm text-gray-400">
                          {empresa.country}
                        </span>
                      </div>
                      <p className="text-gray-300 mb-3">
                        {empresa.description}
                      </p>
                      <div className="flex items-center space-x-4 mb-3">
                        <span className="text-sm text-gray-400">
                          <Calendar className="h-4 w-4 inline mr-1" />
                          Fundada em {empresa.founded}
                        </span>
                      </div>
                      <div className="flex flex-wrap gap-2">
                        {empresa.games.map((game, index) => (
                          <span
                            key={index}
                            className="px-3 py-1 bg-purple-600/20 text-purple-300 rounded-full text-sm"
                          >
                            {game}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          )}

          {activeTab === "games" && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {games.map((game) => (
                <motion.div
                  key={game.id}
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  className="glass-card"
                >
                  <div className="flex space-x-4">
                    <img
                      src={game.image_url}
                      alt={game.name}
                      className="w-16 h-16 rounded-lg object-cover"
                    />
                    <div className="flex-1">
                      <h3 className="font-semibold text-white">{game.name}</h3>
                      <p className="text-sm text-gray-400">{game.publisher}</p>
                      <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                        <span className="flex items-center">
                          <Clock className="h-3 w-3 mr-1" />
                          25h played
                        </span>
                        <span className="flex items-center">
                          <Trophy className="h-3 w-3 mr-1" />
                          8/12 achievements
                        </span>
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          )}

          {activeTab === "config-pc-rank" && (
            <div className="max-w-2xl mx-auto">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="glass-card"
              >
                <div className="flex items-center mb-6">
                  <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-blue-500 rounded-lg flex items-center justify-center mr-4">
                    <span className="text-2xl">🖥️</span>
                  </div>
                  <div>
                    <h2 className="text-2xl font-bold text-white">
                      Config PC Rank
                    </h2>
                    <p className="text-gray-400">
                      Configure as especificações do seu PC para ranking
                    </p>
                  </div>
                </div>

                <div className="space-y-6">
                  {/* Processador */}
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Processador (CPU)
                    </label>
                    <select
                      value={formData.cpu}
                      onChange={(e) => handleInputChange("cpu", e.target.value)}
                      className="w-full p-3 bg-gray-800/50 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-blue-500"
                    >
                      <option value="">Selecione seu processador</option>
                      <option value="intel-i9-13900k">
                        Intel Core i9-13900K 💎
                      </option>
                      <option value="intel-i7-13700k">
                        Intel Core i7-13700K 🥇
                      </option>
                      <option value="intel-i5-13600k">
                        Intel Core i5-13600K 🥈
                      </option>
                      <option value="amd-ryzen-9-7950x">
                        AMD Ryzen 9 7950X 💎
                      </option>
                      <option value="amd-ryzen-7-7700x">
                        AMD Ryzen 7 7700X 🥇
                      </option>
                      <option value="amd-ryzen-5-7600x">
                        AMD Ryzen 5 7600X 🥈
                      </option>
                      <option value="outros">Outros 🥉</option>
                    </select>
                  </div>

                  {/* Placa de Vídeo */}
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Placa de Vídeo (GPU)
                    </label>
                    <select
                      value={formData.gpu}
                      onChange={(e) => handleInputChange("gpu", e.target.value)}
                      className="w-full p-3 bg-gray-800/50 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-blue-500"
                    >
                      <option value="">Selecione sua placa de vídeo</option>
                      <option value="rtx-4090">NVIDIA RTX 4090 💎</option>
                      <option value="rtx-4080">NVIDIA RTX 4080 💎</option>
                      <option value="rtx-4070-ti">NVIDIA RTX 4070 Ti 🥇</option>
                      <option value="rtx-4070">NVIDIA RTX 4070 🥇</option>
                      <option value="rtx-3080">NVIDIA RTX 3080 🥈</option>
                      <option value="rtx-3070">NVIDIA RTX 3070 🥈</option>
                      <option value="rx-7900-xtx">AMD RX 7900 XTX 💎</option>
                      <option value="rx-7800-xt">AMD RX 7800 XT 🥇</option>
                      <option value="outros-gpu">Outros 🥉</option>
                    </select>
                  </div>

                  {/* Memória RAM */}
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Memória RAM
                    </label>
                    <select
                      value={formData.ram}
                      onChange={(e) => handleInputChange("ram", e.target.value)}
                      className="w-full p-3 bg-gray-800/50 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-blue-500"
                    >
                      <option value="">Selecione a quantidade de RAM</option>
                      <option value="64gb">64GB DDR5 💎</option>
                      <option value="32gb-ddr5">32GB DDR5 🥇</option>
                      <option value="32gb-ddr4">32GB DDR4 🥇</option>
                      <option value="16gb-ddr5">16GB DDR5 🥈</option>
                      <option value="16gb-ddr4">16GB DDR4 🥈</option>
                      <option value="8gb">8GB 🥉</option>
                      <option value="menos-8gb">Menos de 8GB 💩</option>
                    </select>
                  </div>

                  {/* Armazenamento */}
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Armazenamento Principal
                    </label>
                    <select
                      value={formData.storage}
                      onChange={(e) =>
                        handleInputChange("storage", e.target.value)
                      }
                      className="w-full p-3 bg-gray-800/50 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-blue-500"
                    >
                      <option value="">Selecione seu armazenamento</option>
                      <option value="nvme-gen4">NVMe SSD Gen4 💎</option>
                      <option value="nvme-gen3">NVMe SSD Gen3 🥇</option>
                      <option value="sata-ssd">SATA SSD 🥈</option>
                      <option value="hdd">HDD 🥉</option>
                      <option value="hdd-antigo">HDD Antigo 💩</option>
                    </select>
                  </div>

                  {/* Rank Atual */}
                  <div className="bg-gray-800/30 rounded-lg p-4 border border-gray-700">
                    <h3 className="text-lg font-semibold text-white mb-3">
                      Seu Rank Atual
                    </h3>
                    <div className="flex items-center justify-center">
                      <div className="text-center">
                        <div className="text-6xl mb-2">
                          {pcConfig.rank.emoji}
                        </div>
                        <div className="text-xl font-bold text-white">
                          {pcConfig.rank.name}
                        </div>
                        <div className="text-sm text-gray-400">
                          {pcConfig.cpu
                            ? "Configuração salva!"
                            : "Configure seu PC para calcular o rank"}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Mensagem de Sucesso */}
                  {showSuccess && (
                    <motion.div
                      initial={{ opacity: 0, y: -10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="bg-green-600/20 border border-green-600/40 rounded-lg p-4 text-center"
                    >
                      <div className="text-green-400 font-semibold">
                        ✅ Configuração salva com sucesso!
                      </div>
                      <div className="text-sm text-green-300 mt-1">
                        Seu rank foi atualizado para {pcConfig.rank.emoji}{" "}
                        {pcConfig.rank.name}
                      </div>
                    </motion.div>
                  )}

                  {/* Botões */}
                  <div className="flex space-x-4">
                    <button
                      onClick={handleSaveConfig}
                      className="flex-1 btn-primary"
                    >
                      Salvar Configuração
                    </button>
                    <button
                      onClick={handleSaveConfig}
                      className="flex-1 btn-secondary"
                    >
                      Calcular Rank
                    </button>
                  </div>

                  {/* Info sobre Ranking */}
                  <div className="bg-blue-600/10 border border-blue-600/20 rounded-lg p-4">
                    <h4 className="text-blue-300 font-semibold mb-2">
                      Como funciona o Ranking?
                    </h4>
                    <div className="text-sm text-gray-300 space-y-1">
                      <div>
                        💎 <strong>Diamond:</strong> PC Top de linha (RTX 4090,
                        i9-13900K)
                      </div>
                      <div>
                        🥇 <strong>Gold:</strong> PC High-end (RTX 4070+, i7+)
                      </div>
                      <div>
                        🥈 <strong>Silver:</strong> PC Mid-range (RTX 3070+,
                        i5+)
                      </div>
                      <div>
                        🥉 <strong>Bronze:</strong> PC Entry-level (GTX 1660+)
                      </div>
                      <div>
                        💩 <strong>Potato:</strong> PC que precisa de upgrade
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            </div>
          )}

          {(activeTab === "achievements" ||
            activeTab === "highlights" ||
            activeTab === "media") && (
            <div className="text-center py-12">
              <Gamepad2 className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-white mb-2">
                {activeTab === "achievements" && "No achievements yet"}
                {activeTab === "highlights" && "No highlights yet"}
                {activeTab === "media" && "No media yet"}
              </h3>
              <p className="text-gray-400">
                {activeTab === "achievements" &&
                  "Start playing games to unlock achievements!"}
                {activeTab === "highlights" &&
                  "Share your best gaming moments!"}
                {activeTab === "media" && "Upload screenshots and videos!"}
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Profile;
