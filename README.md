# 🎮 PROJETO-PRINCIPAL-GAMES

Um sistema completo de gerenciamento de jogos e perfis de usuário com ranking de PC e biblioteca pessoal.

## 🚀 Funcionalidades

### 👤 Sistema de Usuários
- **Registro e Login** com username/password
- **Perfis personalizáveis** com avatar e banner
- **Banners dinâmicos** (imagens, GIFs e vídeos)
- **Empresa favorita** configurável

### 🎮 Biblioteca de Jogos
- **Busca de jogos** da Steam (simulada)
- **Adição manual** de jogos
- **Biblioteca pessoal** com status de completado
- **Imagens automáticas** dos jogos via Steam API
- **Gerenciamento completo** (adicionar, remover, marcar como completado)

### 💻 PC Setup Completo
- **Componentes principais**: GPU, CPU, RAM, Storage, Placa Mã<PERSON>, Gabinete, <PERSON><PERSON>, <PERSON>onte
- **Múltiplos monitores** com modelo, tamanho e resolução
- **Periféricos**: Te<PERSON>lad<PERSON>, Mouse, Headset, Caixas de Som
- **Ranking automático** baseado nas especificações

### 🏆 Sistema de Rankings
- **Top Players** baseado no total de jogos
- **Top PC** baseado nas especificações do computador
- **Ranks visuais** com emojis (💎 Diamond, 🥇 Gold, 🥈 Silver, 🥉 Bronze, 💩)
- **Pontuação automática** calculada pelo sistema

### 👥 Perfis Públicos
- **Visualização de perfis** clicando no nome do usuário
- **Biblioteca completa** do usuário
- **Jogos completados** em destaque
- **Setup completo** do PC
- **Banner personalizado** como fundo

### 🔄 Sincronização Real
- **Servidor backend** Node.js com API REST
- **Banco de dados** JSON sincronizado
- **Atualizações em tempo real** entre usuários
- **Suporte a múltiplos PCs** via Radmin/LAN

## 🛠️ Tecnologias

### Frontend
- **React 18** com Hooks
- **React Router** para navegação
- **Vite** como bundler
- **CSS puro** (sem frameworks)

### Backend
- **Node.js** com Express
- **API REST** completa
- **Banco JSON** para persistência
- **CORS** habilitado

### Recursos
- **Steam API** simulada com 100+ jogos
- **Placeholders** automáticos para imagens
- **Responsive design** para mobile/desktop
- **Hot reload** para desenvolvimento

## 📦 Instalação

### Pré-requisitos
- Node.js 16+ 
- NPM ou Yarn

### 1. Clone o repositório
```bash
git clone https://github.com/SEU-USUARIO/PROJETO-PRINCIPAL-GAMES.git
cd PROJETO-PRINCIPAL-GAMES
```

### 2. Instale as dependências
```bash
# Frontend
npm install

# Backend
cd server
npm install
```

### 3. Inicie os servidores

**Terminal 1 - Backend:**
```bash
cd server
npm start
```

**Terminal 2 - Frontend:**
```bash
npm run dev
```

## 🌐 Acesso

### Local
- **Frontend**: http://localhost:3000
- **Backend**: http://localhost:3001

### Rede (Radmin/LAN)
- **Frontend**: http://SEU-IP:3000
- **Backend**: http://SEU-IP:3001

## 👤 Usuário Admin

Para acessar funcionalidades administrativas:
- **Usuário**: admin
- **Senha**: admin

## 📁 Estrutura do Projeto

```
PROJETO-PRINCIPAL-GAMES/
├── src/                    # Frontend React
│   ├── components/         # Componentes reutilizáveis
│   ├── context/           # Context API (AuthContext)
│   ├── hooks/             # Custom hooks
│   ├── pages/             # Páginas da aplicação
│   ├── services/          # Serviços (DatabaseService)
│   └── styles/            # Estilos CSS
├── server/                # Backend Node.js
│   ├── data/              # Banco de dados JSON
│   └── server.js          # Servidor principal
├── public/                # Arquivos estáticos
└── package.json           # Dependências
```

## 🎮 Como Usar

1. **Registre-se** ou faça login
2. **Configure seu perfil** com avatar e banner
3. **Adicione jogos** via busca ou manualmente
4. **Configure seu PC** com todas as especificações
5. **Veja os rankings** e compare com outros usuários
6. **Explore perfis** clicando nos nomes dos usuários

## 🔧 Desenvolvimento

### Scripts Disponíveis
```bash
npm run dev          # Inicia desenvolvimento
npm run build        # Build para produção
npm run preview      # Preview da build
npm run lint         # Linting do código
```

### Portas Configuradas
- **Frontend**: 3000 (configurado no vite.config.js)
- **Backend**: 3001 (configurado no server.js)

## 🤝 Contribuição

1. Fork o projeto
2. Crie uma branch para sua feature
3. Commit suas mudanças
4. Push para a branch
5. Abra um Pull Request

## 📄 Licença

Este projeto está sob a licença MIT.

## 🎯 Roadmap

- [ ] Sistema de amigos
- [ ] Chat em tempo real
- [ ] Integração com Steam API real
- [ ] Sistema de conquistas
- [ ] Modo escuro/claro
- [ ] PWA (Progressive Web App)

---

**Desenvolvido com ❤️ para gamers!** 🎮
