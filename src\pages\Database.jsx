import React, { useState } from "react";
import { useAuth } from "../context/AuthContext";
import databaseService from "../services/DatabaseService";

const Database = () => {
  const { getAllUsers, user, getLogs } = useAuth();
  const [cleanupResult, setCleanupResult] = useState(null);
  const users = getAllUsers();
  const logs = getLogs();

  const clearDatabase = () => {
    if (window.confirm("Tem certeza que deseja limpar o banco de dados?")) {
      localStorage.removeItem("usersDatabase");
      localStorage.removeItem("currentUser");
      localStorage.removeItem("gamesDatabase");
      window.location.reload();
    }
  };

  const clearLogs = () => {
    if (window.confirm("Tem certeza que deseja limpar os logs?")) {
      localStorage.removeItem("systemLogs");
      window.location.reload();
    }
  };

  const cleanupDuplicates = async () => {
    if (
      window.confirm(
        "Tem certeza que deseja remover dados duplicados do servidor?"
      )
    ) {
      try {
        const result = await databaseService.cleanupDuplicates();
        setCleanupResult(result);
        setTimeout(() => setCleanupResult(null), 10000); // Limpar mensagem após 10s
      } catch (error) {
        console.error("Erro ao limpar duplicatas:", error);
        alert("Erro ao limpar duplicatas. Verifique o console.");
      }
    }
  };

  // Verificar se é administrador
  if (!user || !user.isAdmin) {
    return (
      <div className="main-content">
        <h1>Acesso Negado</h1>
        <p>Apenas administradores podem acessar esta página.</p>
        <p>
          Use: <strong>usuário: admin, senha: admin</strong>
        </p>
      </div>
    );
  }

  return (
    <div className="main-content">
      <h1>Banco de Dados - Debug</h1>

      <div>
        <h2>Usuários Cadastrados ({users.length})</h2>

        {users.length === 0 ? (
          <p>Nenhum usuário cadastrado</p>
        ) : (
          <table
            border="1"
            style={{ width: "100%", borderCollapse: "collapse" }}
          >
            <thead>
              <tr>
                <th>ID</th>
                <th>Nome</th>
                <th>Usuário</th>
                <th>Senha</th>
                <th>Criado em</th>
              </tr>
            </thead>
            <tbody>
              {users.map((user) => (
                <tr key={user.id}>
                  <td>{user.id}</td>
                  <td>{user.name}</td>
                  <td>{user.username}</td>
                  <td>{user.password}</td>
                  <td>{new Date(user.createdAt).toLocaleString("pt-BR")}</td>
                </tr>
              ))}
            </tbody>
          </table>
        )}
      </div>

      <div style={{ marginTop: "20px" }}>
        <button
          onClick={clearDatabase}
          style={{
            backgroundColor: "red",
            color: "white",
            marginRight: "10px",
          }}
        >
          Limpar Banco de Dados
        </button>
        <button
          onClick={clearLogs}
          style={{
            backgroundColor: "orange",
            color: "white",
            marginRight: "10px",
          }}
        >
          Limpar Logs
        </button>
        <button
          onClick={cleanupDuplicates}
          style={{ backgroundColor: "#6f42c1", color: "white" }}
        >
          🧹 Limpar Duplicatas
        </button>
      </div>

      {/* Resultado da limpeza */}
      {cleanupResult && (
        <div
          style={{
            marginTop: "20px",
            padding: "15px",
            backgroundColor: "#d4edda",
            border: "1px solid #c3e6cb",
            borderRadius: "5px",
            color: "#155724",
          }}
        >
          <h3>✅ {cleanupResult.message}</h3>
          <p>
            <strong>Removidos:</strong>
          </p>
          <ul>
            <li>Usuários: {cleanupResult.removed.users}</li>
            <li>Jogos: {cleanupResult.removed.games}</li>
            <li>Logs: {cleanupResult.removed.logs}</li>
          </ul>
          <p>
            <strong>Restantes:</strong>
          </p>
          <ul>
            <li>Usuários: {cleanupResult.remaining.users}</li>
            <li>Jogos: {cleanupResult.remaining.games}</li>
            <li>Logs: {cleanupResult.remaining.logs}</li>
          </ul>
        </div>
      )}

      {/* Seção de Logs */}
      <div style={{ marginTop: "30px" }}>
        <h2>Logs do Sistema ({logs.length})</h2>

        {logs.length === 0 ? (
          <p>Nenhum log registrado</p>
        ) : (
          <div
            style={{
              backgroundColor: "#f8f9fa",
              border: "1px solid #dee2e6",
              borderRadius: "8px",
              padding: "1rem",
              marginTop: "1rem",
              maxHeight: "400px",
              overflowY: "auto",
            }}
          >
            {logs.map((log) => (
              <div
                key={log.id}
                style={{
                  padding: "0.5rem 0",
                  borderBottom: "1px solid #e9ecef",
                  fontSize: "0.9rem",
                }}
              >
                <div
                  style={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                  }}
                >
                  <span
                    style={{
                      fontWeight: "bold",
                      color: getLogColor(log.action),
                    }}
                  >
                    {log.action}
                  </span>
                  <span style={{ color: "#6c757d", fontSize: "0.8rem" }}>
                    {new Date(log.timestamp).toLocaleString("pt-BR")}
                  </span>
                </div>
                <div style={{ color: "#495057", marginTop: "0.25rem" }}>
                  <strong>{log.user}:</strong> {log.details}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      <div style={{ marginTop: "20px" }}>
        <h3>Dados Raw - Usuários:</h3>
        <pre
          style={{ background: "#f5f5f5", padding: "10px", overflow: "auto" }}
        >
          {JSON.stringify(users, null, 2)}
        </pre>
      </div>

      <div style={{ marginTop: "20px" }}>
        <h3>Dados Raw - Logs:</h3>
        <pre
          style={{ background: "#f5f5f5", padding: "10px", overflow: "auto" }}
        >
          {JSON.stringify(logs, null, 2)}
        </pre>
      </div>
    </div>
  );
};

// Função para obter cor do log baseado na ação
const getLogColor = (action) => {
  switch (action) {
    case "ADMIN_LOGIN":
      return "#dc3545";
    case "USER_LOGIN":
      return "#28a745";
    case "USER_REGISTER":
      return "#007bff";
    case "PROFILE_UPDATE":
      return "#ffc107";
    case "GAME_ADDED":
      return "#17a2b8";
    default:
      return "#6c757d";
  }
};

export default Database;
