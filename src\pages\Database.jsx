import React from 'react';
import { useAuth } from '../context/AuthContext';

const Database = () => {
  const { getAllUsers } = useAuth();
  const users = getAllUsers();

  const clearDatabase = () => {
    if (window.confirm('Tem certeza que deseja limpar o banco de dados?')) {
      localStorage.removeItem('usersDatabase');
      localStorage.removeItem('currentUser');
      window.location.reload();
    }
  };

  return (
    <div className="main-content">
      <h1>Banco de Dados - Debug</h1>
      
      <div>
        <h2>Usuários Cadastrados ({users.length})</h2>
        
        {users.length === 0 ? (
          <p>Nenhum usuário cadastrado</p>
        ) : (
          <table border="1" style={{width: '100%', borderCollapse: 'collapse'}}>
            <thead>
              <tr>
                <th>ID</th>
                <th>Nome</th>
                <th>Usuá<PERSON></th>
                <th><PERSON>ha</th>
                <th><PERSON>riad<PERSON> em</th>
              </tr>
            </thead>
            <tbody>
              {users.map(user => (
                <tr key={user.id}>
                  <td>{user.id}</td>
                  <td>{user.name}</td>
                  <td>{user.username}</td>
                  <td>{user.password}</td>
                  <td>{new Date(user.createdAt).toLocaleString('pt-BR')}</td>
                </tr>
              ))}
            </tbody>
          </table>
        )}
      </div>

      <div style={{marginTop: '20px'}}>
        <button onClick={clearDatabase} style={{backgroundColor: 'red', color: 'white'}}>
          Limpar Banco de Dados
        </button>
      </div>

      <div style={{marginTop: '20px'}}>
        <h3>Dados Raw:</h3>
        <pre style={{background: '#f5f5f5', padding: '10px', overflow: 'auto'}}>
          {JSON.stringify(users, null, 2)}
        </pre>
      </div>
    </div>
  );
};

export default Database;
