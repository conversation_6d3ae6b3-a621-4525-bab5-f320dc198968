import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "../context/AuthContext";

const EditProfile = () => {
  const { user, updateProfile } = useAuth();
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    name: user?.name || "",
    avatar: user?.avatar || "",
    favoriteCompany: user?.favoriteCompany || "",
    banner: user?.banner || "",
    pcRank: user?.pcRank || {
      rank: "Bronze",
      emoji: "🥉",
      points: 0,
    },
  });
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");

  const handleSubmit = (e) => {
    e.preventDefault();
    setError("");
    setSuccess("");

    if (!formData.name.trim()) {
      setError("Nome é obrigatório");
      return;
    }

    const result = updateProfile(formData);
    if (result.success) {
      setSuccess("Perfil atualizado com sucesso!");
      setTimeout(() => {
        navigate("/profile");
      }, 2000);
    } else {
      setError(result.message || "Erro ao atualizar perfil");
    }
  };

  const generateAvatar = () => {
    const name = formData.name || user?.name || "User";
    const colors = [
      "007bff",
      "28a745",
      "dc3545",
      "ffc107",
      "17a2b8",
      "6f42c1",
      "fd7e14",
    ];
    const randomColor = colors[Math.floor(Math.random() * colors.length)];
    const newAvatar = `https://ui-avatars.com/api/?name=${encodeURIComponent(
      name
    )}&background=${randomColor}&color=fff&size=200`;
    setFormData({ ...formData, avatar: newAvatar });
  };

  const updatePcRank = (newRank) => {
    const ranks = {
      Bronze: { emoji: "🥉", points: 0 },
      Prata: { emoji: "🥈", points: 100 },
      Ouro: { emoji: "🥇", points: 200 },
      Diamante: { emoji: "💎", points: 300 },
      Lixo: { emoji: "💩", points: -50 },
    };

    setFormData({
      ...formData,
      pcRank: {
        rank: newRank,
        emoji: ranks[newRank].emoji,
        points: ranks[newRank].points,
      },
    });
  };

  if (!user) {
    return (
      <div className="main-content">
        <h1>Acesso Negado</h1>
        <p>Você precisa estar logado para editar o perfil.</p>
      </div>
    );
  }

  return (
    <div className="main-content">
      <h1>Editar Perfil</h1>

      <form onSubmit={handleSubmit}>
        <div>
          <label>Nome:</label>
          <input
            type="text"
            value={formData.name}
            onChange={(e) => setFormData({ ...formData, name: e.target.value })}
            placeholder="Digite seu nome"
            required
          />
        </div>

        <div>
          <label>Avatar (URL da imagem):</label>
          <input
            type="url"
            value={formData.avatar}
            onChange={(e) =>
              setFormData({ ...formData, avatar: e.target.value })
            }
            placeholder="https://exemplo.com/avatar.jpg"
          />
          <div style={{ marginTop: "10px" }}>
            <button
              type="button"
              onClick={generateAvatar}
              style={{
                backgroundColor: "#6c757d",
                color: "white",
                padding: "8px 16px",
                border: "none",
                borderRadius: "4px",
                cursor: "pointer",
                fontSize: "14px",
              }}
            >
              Gerar Avatar Automático
            </button>
          </div>
        </div>

        {/* Preview do Avatar */}
        {formData.avatar && (
          <div>
            <label>Preview do Avatar:</label>
            <div style={{ marginTop: "10px" }}>
              <img
                src={formData.avatar}
                alt="Preview"
                style={{
                  width: "100px",
                  height: "100px",
                  borderRadius: "50%",
                  objectFit: "cover",
                  border: "2px solid #dee2e6",
                }}
                onError={(e) => {
                  e.target.src =
                    "https://ui-avatars.com/api/?name=Error&background=dc3545&color=fff";
                }}
              />
            </div>
          </div>
        )}

        <div>
          <label>Empresa Favorita:</label>
          <input
            type="text"
            value={formData.favoriteCompany}
            onChange={(e) =>
              setFormData({ ...formData, favoriteCompany: e.target.value })
            }
            placeholder="Ex: CD PROJEKT RED, Rockstar Games, etc."
          />
        </div>

        <div>
          <label>Banner (URL - Imagem, GIF ou Vídeo):</label>
          <input
            type="url"
            value={formData.banner}
            onChange={(e) =>
              setFormData({ ...formData, banner: e.target.value })
            }
            placeholder="https://exemplo.com/banner.jpg"
          />
          {formData.banner && (
            <div style={{ marginTop: "10px" }}>
              <label>Preview do Banner:</label>
              <div
                style={{
                  marginTop: "10px",
                  border: "2px solid #dee2e6",
                  borderRadius: "8px",
                  overflow: "hidden",
                  maxWidth: "400px",
                  height: "150px",
                }}
              >
                {formData.banner.includes(".mp4") ||
                formData.banner.includes(".webm") ? (
                  <video
                    src={formData.banner}
                    style={{
                      width: "100%",
                      height: "100%",
                      objectFit: "cover",
                    }}
                    autoPlay
                    muted
                    loop
                    onError={(e) => {
                      e.target.style.display = "none";
                    }}
                  />
                ) : (
                  <img
                    src={formData.banner}
                    alt="Banner Preview"
                    style={{
                      width: "100%",
                      height: "100%",
                      objectFit: "cover",
                    }}
                    onError={(e) => {
                      e.target.src =
                        "https://via.placeholder.com/400x150?text=Erro+ao+carregar+banner";
                    }}
                  />
                )}
              </div>
            </div>
          )}
        </div>

        <div>
          <label>PC Rank:</label>
          <div
            style={{
              display: "flex",
              gap: "10px",
              flexWrap: "wrap",
              marginTop: "10px",
            }}
          >
            {["Bronze", "Prata", "Ouro", "Diamante", "Lixo"].map((rank) => (
              <button
                key={rank}
                type="button"
                onClick={() => updatePcRank(rank)}
                style={{
                  padding: "8px 16px",
                  border:
                    formData.pcRank.rank === rank
                      ? "2px solid #007bff"
                      : "1px solid #ccc",
                  borderRadius: "4px",
                  backgroundColor:
                    formData.pcRank.rank === rank ? "#e7f3ff" : "white",
                  cursor: "pointer",
                  fontSize: "14px",
                }}
              >
                {rank === "Bronze" && "🥉"}
                {rank === "Prata" && "🥈"}
                {rank === "Ouro" && "🥇"}
                {rank === "Diamante" && "💎"}
                {rank === "Lixo" && "💩"}
                {rank}
              </button>
            ))}
          </div>
          <div style={{ marginTop: "10px", fontSize: "14px", color: "#666" }}>
            Rank atual: {formData.pcRank.emoji} {formData.pcRank.rank} (
            {formData.pcRank.points} pontos)
          </div>
        </div>

        {error && (
          <div
            style={{
              color: "#dc3545",
              backgroundColor: "#f8d7da",
              border: "1px solid #f5c6cb",
              padding: "10px",
              borderRadius: "4px",
              marginBottom: "15px",
            }}
          >
            {error}
          </div>
        )}

        {success && (
          <div
            style={{
              color: "#155724",
              backgroundColor: "#d4edda",
              border: "1px solid #c3e6cb",
              padding: "10px",
              borderRadius: "4px",
              marginBottom: "15px",
            }}
          >
            {success}
          </div>
        )}

        <div style={{ display: "flex", gap: "10px" }}>
          <button type="submit" className="btn btn-primary">
            Salvar Alterações
          </button>
          <button
            type="button"
            onClick={() => navigate("/profile")}
            className="btn btn-secondary"
          >
            Cancelar
          </button>
        </div>
      </form>
    </div>
  );
};

export default EditProfile;
