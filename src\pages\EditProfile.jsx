import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "../context/AuthContext";

const EditProfile = () => {
  const { user, updateProfile, calculatePcRank } = useAuth();
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    name: user?.name || "",
    avatar: user?.avatar || "",
    favoriteCompany: user?.favoriteCompany || "",
    banner: user?.banner || "",
    pcSpecs: user?.pcSpecs || {
      gpu: "",
      cpu: "",
      ram: "",
      storage: "",
      motherboard: "",
      case: "",
      cooler: "",
      psu: "",
      monitors: [{ model: "", size: "", resolution: "" }],
      keyboard: "",
      mouse: "",
      headset: "",
      speakers: "",
    },
    pcRank: user?.pcRank || {
      rank: "Bronze",
      emoji: "🥉",
      points: 0,
    },
  });
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");

  const handleSubmit = (e) => {
    e.preventDefault();
    setError("");
    setSuccess("");

    if (!formData.name.trim()) {
      setError("Nome é obrigatório");
      return;
    }

    // Calcular PC Rank automaticamente baseado nas especificações
    const calculatedRank = calculatePcRank(formData.pcSpecs);
    const updatedFormData = {
      ...formData,
      pcRank: calculatedRank,
    };

    const result = updateProfile(updatedFormData);
    if (result.success) {
      setSuccess("Perfil atualizado com sucesso!");
      setTimeout(() => {
        navigate("/profile");
      }, 2000);
    } else {
      setError(result.message || "Erro ao atualizar perfil");
    }
  };

  const generateAvatar = () => {
    const name = formData.name || user?.name || "User";
    const colors = [
      "007bff",
      "28a745",
      "dc3545",
      "ffc107",
      "17a2b8",
      "6f42c1",
      "fd7e14",
    ];
    const randomColor = colors[Math.floor(Math.random() * colors.length)];
    const newAvatar = `https://ui-avatars.com/api/?name=${encodeURIComponent(
      name
    )}&background=${randomColor}&color=fff&size=200`;
    setFormData({ ...formData, avatar: newAvatar });
  };

  // Função para atualizar especificações do PC e recalcular rank
  const updatePcSpecs = (field, value) => {
    const newSpecs = { ...formData.pcSpecs, [field]: value };
    const calculatedRank = calculatePcRank(newSpecs);

    setFormData({
      ...formData,
      pcSpecs: newSpecs,
      pcRank: calculatedRank,
    });
  };

  // Função para adicionar monitor
  const addMonitor = () => {
    const newMonitors = [
      ...formData.pcSpecs.monitors,
      { model: "", size: "", resolution: "" },
    ];
    updatePcSpecs("monitors", newMonitors);
  };

  // Função para remover monitor
  const removeMonitor = (index) => {
    const newMonitors = formData.pcSpecs.monitors.filter((_, i) => i !== index);
    updatePcSpecs("monitors", newMonitors);
  };

  // Função para atualizar monitor específico
  const updateMonitor = (index, field, value) => {
    const newMonitors = [...formData.pcSpecs.monitors];
    newMonitors[index] = { ...newMonitors[index], [field]: value };
    updatePcSpecs("monitors", newMonitors);
  };

  if (!user) {
    return (
      <div className="main-content">
        <h1>Acesso Negado</h1>
        <p>Você precisa estar logado para editar o perfil.</p>
      </div>
    );
  }

  return (
    <div className="main-content">
      <h1>Editar Perfil</h1>

      <form onSubmit={handleSubmit}>
        <div>
          <label>Nome:</label>
          <input
            type="text"
            value={formData.name}
            onChange={(e) => setFormData({ ...formData, name: e.target.value })}
            placeholder="Digite seu nome"
            required
          />
        </div>

        <div>
          <label>Avatar (URL da imagem):</label>
          <input
            type="url"
            value={formData.avatar}
            onChange={(e) =>
              setFormData({ ...formData, avatar: e.target.value })
            }
            placeholder="https://exemplo.com/avatar.jpg"
          />
          <div style={{ marginTop: "10px" }}>
            <button
              type="button"
              onClick={generateAvatar}
              style={{
                backgroundColor: "#6c757d",
                color: "white",
                padding: "8px 16px",
                border: "none",
                borderRadius: "4px",
                cursor: "pointer",
                fontSize: "14px",
              }}
            >
              Gerar Avatar Automático
            </button>
          </div>
        </div>

        {/* Preview do Avatar */}
        {formData.avatar && (
          <div>
            <label>Preview do Avatar:</label>
            <div style={{ marginTop: "10px" }}>
              <img
                src={formData.avatar}
                alt="Preview"
                style={{
                  width: "100px",
                  height: "100px",
                  borderRadius: "50%",
                  objectFit: "cover",
                  border: "2px solid #dee2e6",
                }}
                onError={(e) => {
                  e.target.src =
                    "https://ui-avatars.com/api/?name=Error&background=dc3545&color=fff";
                }}
              />
            </div>
          </div>
        )}

        <div>
          <label>Empresa Favorita:</label>
          <input
            type="text"
            value={formData.favoriteCompany}
            onChange={(e) =>
              setFormData({ ...formData, favoriteCompany: e.target.value })
            }
            placeholder="Ex: CD PROJEKT RED, Rockstar Games, etc."
          />
        </div>

        <div>
          <label>Banner (URL - Imagem, GIF ou Vídeo):</label>
          <input
            type="url"
            value={formData.banner}
            onChange={(e) =>
              setFormData({ ...formData, banner: e.target.value })
            }
            placeholder="https://exemplo.com/banner.jpg"
          />
          {formData.banner && (
            <div style={{ marginTop: "10px" }}>
              <label>Preview do Banner:</label>
              <div
                style={{
                  marginTop: "10px",
                  border: "2px solid #dee2e6",
                  borderRadius: "8px",
                  overflow: "hidden",
                  maxWidth: "400px",
                  height: "150px",
                }}
              >
                {formData.banner.includes(".mp4") ||
                formData.banner.includes(".webm") ? (
                  <video
                    src={formData.banner}
                    style={{
                      width: "100%",
                      height: "100%",
                      objectFit: "cover",
                    }}
                    autoPlay
                    muted
                    loop
                    onError={(e) => {
                      e.target.style.display = "none";
                    }}
                  />
                ) : (
                  <img
                    src={formData.banner}
                    alt="Banner Preview"
                    style={{
                      width: "100%",
                      height: "100%",
                      objectFit: "cover",
                    }}
                    onError={(e) => {
                      e.target.src =
                        "https://via.placeholder.com/400x150?text=Erro+ao+carregar+banner";
                    }}
                  />
                )}
              </div>
            </div>
          )}
        </div>

        <div>
          <h3>Especificações do PC</h3>
          <p style={{ fontSize: "14px", color: "#666", marginBottom: "15px" }}>
            O PC Rank será calculado automaticamente baseado nas especificações
            abaixo:
          </p>

          <div>
            <label>Placa de Vídeo (GPU):</label>
            <input
              type="text"
              value={formData.pcSpecs.gpu}
              onChange={(e) => updatePcSpecs("gpu", e.target.value)}
              placeholder="Ex: RTX 4090, RTX 3080, RX 7900 XTX"
            />
          </div>

          <div>
            <label>Processador (CPU):</label>
            <input
              type="text"
              value={formData.pcSpecs.cpu}
              onChange={(e) => updatePcSpecs("cpu", e.target.value)}
              placeholder="Ex: i9-13900K, Ryzen 9 7950X, i5-12600K"
            />
          </div>

          <div>
            <label>Memória RAM:</label>
            <input
              type="text"
              value={formData.pcSpecs.ram}
              onChange={(e) => updatePcSpecs("ram", e.target.value)}
              placeholder="Ex: 32GB DDR5, 16GB DDR4, 64GB"
            />
          </div>

          <div>
            <label>Armazenamento:</label>
            <input
              type="text"
              value={formData.pcSpecs.storage}
              onChange={(e) => updatePcSpecs("storage", e.target.value)}
              placeholder="Ex: 1TB NVMe SSD, 2TB M.2, 500GB SSD"
            />
          </div>

          <div>
            <label>Placa Mãe:</label>
            <input
              type="text"
              value={formData.pcSpecs.motherboard}
              onChange={(e) => updatePcSpecs("motherboard", e.target.value)}
              placeholder="Ex: ASUS ROG Strix Z790-E, MSI B550 Tomahawk"
            />
          </div>

          <div>
            <label>Gabinete:</label>
            <input
              type="text"
              value={formData.pcSpecs.case}
              onChange={(e) => updatePcSpecs("case", e.target.value)}
              placeholder="Ex: NZXT H7 Elite, Corsair 4000D, Lian Li O11"
            />
          </div>

          <div>
            <label>Cooler/Water Cooler:</label>
            <input
              type="text"
              value={formData.pcSpecs.cooler}
              onChange={(e) => updatePcSpecs("cooler", e.target.value)}
              placeholder="Ex: Corsair H150i, Noctua NH-D15, AIO 240mm"
            />
          </div>

          <div>
            <label>Fonte (PSU):</label>
            <input
              type="text"
              value={formData.pcSpecs.psu}
              onChange={(e) => updatePcSpecs("psu", e.target.value)}
              placeholder="Ex: Corsair RM850x, EVGA 750W Gold, Seasonic 1000W"
            />
          </div>

          {/* Seção de Monitores */}
          <div style={{ marginTop: "2rem" }}>
            <div
              style={{
                display: "flex",
                alignItems: "center",
                gap: "1rem",
                marginBottom: "1rem",
              }}
            >
              <label style={{ fontSize: "1.2rem", fontWeight: "bold" }}>
                Monitores:
              </label>
              <button
                type="button"
                onClick={addMonitor}
                style={{
                  backgroundColor: "#28a745",
                  color: "white",
                  border: "none",
                  padding: "0.5rem 1rem",
                  borderRadius: "4px",
                  cursor: "pointer",
                }}
              >
                + Adicionar Monitor
              </button>
            </div>

            {formData.pcSpecs.monitors.map((monitor, index) => (
              <div
                key={index}
                style={{
                  border: "1px solid #ddd",
                  borderRadius: "8px",
                  padding: "1rem",
                  marginBottom: "1rem",
                  backgroundColor: "#f8f9fa",
                }}
              >
                <div
                  style={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    marginBottom: "1rem",
                  }}
                >
                  <h4 style={{ margin: 0 }}>Monitor {index + 1}</h4>
                  {formData.pcSpecs.monitors.length > 1 && (
                    <button
                      type="button"
                      onClick={() => removeMonitor(index)}
                      style={{
                        backgroundColor: "#dc3545",
                        color: "white",
                        border: "none",
                        padding: "0.25rem 0.5rem",
                        borderRadius: "4px",
                        cursor: "pointer",
                      }}
                    >
                      Remover
                    </button>
                  )}
                </div>

                <div
                  style={{
                    display: "grid",
                    gridTemplateColumns: "1fr 1fr 1fr",
                    gap: "1rem",
                  }}
                >
                  <div>
                    <label>Modelo:</label>
                    <input
                      type="text"
                      value={monitor.model}
                      onChange={(e) =>
                        updateMonitor(index, "model", e.target.value)
                      }
                      placeholder="Ex: LG 27GP950, ASUS PG279Q"
                    />
                  </div>
                  <div>
                    <label>Tamanho:</label>
                    <input
                      type="text"
                      value={monitor.size}
                      onChange={(e) =>
                        updateMonitor(index, "size", e.target.value)
                      }
                      placeholder="Ex: 27'', 24'', 32''"
                    />
                  </div>
                  <div>
                    <label>Resolução:</label>
                    <input
                      type="text"
                      value={monitor.resolution}
                      onChange={(e) =>
                        updateMonitor(index, "resolution", e.target.value)
                      }
                      placeholder="Ex: 4K, 1440p, 1080p"
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Seção de Periféricos */}
          <div style={{ marginTop: "2rem" }}>
            <h3 style={{ marginBottom: "1rem", color: "#495057" }}>
              🎮 Periféricos
            </h3>

            <div>
              <label>Teclado:</label>
              <input
                type="text"
                value={formData.pcSpecs.keyboard}
                onChange={(e) => updatePcSpecs("keyboard", e.target.value)}
                placeholder="Ex: Logitech G Pro X, Razer BlackWidow V3"
              />
            </div>

            <div>
              <label>Mouse:</label>
              <input
                type="text"
                value={formData.pcSpecs.mouse}
                onChange={(e) => updatePcSpecs("mouse", e.target.value)}
                placeholder="Ex: Logitech G Pro X Superlight, Razer DeathAdder V3"
              />
            </div>

            <div>
              <label>Headset/Fone:</label>
              <input
                type="text"
                value={formData.pcSpecs.headset}
                onChange={(e) => updatePcSpecs("headset", e.target.value)}
                placeholder="Ex: SteelSeries Arctis 7, HyperX Cloud II"
              />
            </div>

            <div>
              <label>Caixas de Som:</label>
              <input
                type="text"
                value={formData.pcSpecs.speakers}
                onChange={(e) => updatePcSpecs("speakers", e.target.value)}
                placeholder="Ex: Logitech Z623, Creative Pebble V3"
              />
            </div>
          </div>

          {/* PC Rank Calculado */}
          <div
            style={{
              backgroundColor: "#f8f9fa",
              border: "2px solid #dee2e6",
              borderRadius: "8px",
              padding: "1rem",
              marginTop: "15px",
              textAlign: "center",
            }}
          >
            <h4 style={{ margin: "0 0 0.5rem 0", color: "#495057" }}>
              PC Rank Calculado
            </h4>
            <div style={{ fontSize: "2rem", marginBottom: "0.5rem" }}>
              {formData.pcRank.emoji}
            </div>
            <div
              style={{
                fontSize: "1.2rem",
                fontWeight: "bold",
                color: "#495057",
              }}
            >
              {formData.pcRank.rank}
            </div>
            <div style={{ fontSize: "0.9rem", color: "#6c757d" }}>
              {formData.pcRank.points} pontos
            </div>
          </div>
        </div>

        {error && (
          <div
            style={{
              color: "#dc3545",
              backgroundColor: "#f8d7da",
              border: "1px solid #f5c6cb",
              padding: "10px",
              borderRadius: "4px",
              marginBottom: "15px",
            }}
          >
            {error}
          </div>
        )}

        {success && (
          <div
            style={{
              color: "#155724",
              backgroundColor: "#d4edda",
              border: "1px solid #c3e6cb",
              padding: "10px",
              borderRadius: "4px",
              marginBottom: "15px",
            }}
          >
            {success}
          </div>
        )}

        <div style={{ display: "flex", gap: "10px" }}>
          <button type="submit" className="btn btn-primary">
            Salvar Alterações
          </button>
          <button
            type="button"
            onClick={() => navigate("/profile")}
            className="btn btn-secondary"
          >
            Cancelar
          </button>
        </div>
      </form>
    </div>
  );
};

export default EditProfile;
