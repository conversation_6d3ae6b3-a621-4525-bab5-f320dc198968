[{"id": 1750856609780, "timestamp": "2025-06-25T13:03:29.780Z", "user": "<PERSON><PERSON><PERSON>", "action": "USER_REGISTER", "details": "Novo usuário Erride se registrou no sistema"}, {"id": 1750856609789, "timestamp": "2025-06-25T13:03:29.789Z", "user": "admin", "action": "GAME_ADDED", "details": "Jo<PERSON> \"Red Dead Redemption 2\" adicionado à biblioteca"}, {"id": 1750856609797, "timestamp": "2025-06-25T13:03:29.797Z", "user": "admin", "action": "GAME_ADDED", "details": "Jo<PERSON> \"Red Dead Redemption 2\" adicionado à biblioteca"}, {"id": 1750856609799, "timestamp": "2025-06-25T13:03:29.799Z", "user": "admin", "action": "ADMIN_LOGIN", "details": "Administrador fez login no sistema"}, {"id": 1750856609807, "timestamp": "2025-06-25T13:03:29.807Z", "user": "admin", "action": "ADMIN_LOGIN", "details": "Administrador fez login no sistema"}, {"id": 1750856619142, "timestamp": "2025-06-25T13:03:39.142Z", "user": "admin", "action": "GAME_COMPLETED", "details": "<PERSON><PERSON> \"<PERSON><PERSON>\" marcado como completado"}, {"id": 1750856619157, "timestamp": "2025-06-25T13:03:39.157Z", "user": "admin", "action": "GAME_ADDED", "details": "Jo<PERSON> \"Terraria\" adicionado à biblioteca"}, {"id": 1750856619164, "timestamp": "2025-06-25T13:03:39.164Z", "user": "admin", "action": "ADMIN_LOGIN", "details": "Administrador fez login no sistema"}, {"id": 1750856619165, "timestamp": "2025-06-25T13:03:39.165Z", "user": "admin", "action": "ADMIN_LOGIN", "details": "Administrador fez login no sistema"}, {"id": 1750856619176, "timestamp": "2025-06-25T13:03:39.176Z", "user": "<PERSON><PERSON><PERSON>", "action": "PROFILE_UPDATE", "details": "Perfil atualizado: name, avatar, favoriteCompany, banner, pcSpecs, pcRank"}, {"id": 1750856619178, "timestamp": "2025-06-25T13:03:39.178Z", "user": "<PERSON><PERSON><PERSON>", "action": "PROFILE_UPDATE", "details": "Perfil atualizado: name, avatar, favoriteCompany, banner, pcSpecs, pcRank"}, {"id": 1750856619183, "timestamp": "2025-06-25T13:03:39.183Z", "user": "<PERSON><PERSON><PERSON>", "action": "GAME_ADDED", "details": "Jo<PERSON> \"Red Dead Redemption 2\" adicionado à biblioteca"}, {"id": 1750856619185, "timestamp": "2025-06-25T13:03:39.185Z", "user": "<PERSON><PERSON><PERSON>", "action": "GAME_ADDED", "details": "Jo<PERSON> \"Red Dead Redemption 2\" adicionado à biblioteca"}, {"id": 1750856619193, "timestamp": "2025-06-25T13:03:39.193Z", "user": "<PERSON><PERSON><PERSON>", "action": "PROFILE_UPDATE", "details": "Perfil atualizado: pcRank"}, {"id": 1750856619194, "timestamp": "2025-06-25T13:03:39.194Z", "user": "<PERSON><PERSON><PERSON>", "action": "PROFILE_UPDATE", "details": "Perfil atualizado: pcRank"}, {"id": 1750856619200, "timestamp": "2025-06-25T13:03:39.200Z", "user": "<PERSON><PERSON><PERSON>", "action": "GAME_REMOVED", "details": "Jo<PERSON> \"Cyberpunk 2077\" removido da biblioteca"}, {"id": 1750856619201, "timestamp": "2025-06-25T13:03:39.201Z", "user": "<PERSON><PERSON><PERSON>", "action": "GAME_REMOVED", "details": "Jo<PERSON> \"Cyberpunk 2077\" removido da biblioteca"}, {"id": 1750856619208, "timestamp": "2025-06-25T13:03:39.208Z", "user": "<PERSON><PERSON><PERSON>", "action": "PROFILE_UPDATE", "details": "Perfil atualizado: pcRank"}, {"id": 1750856619209, "timestamp": "2025-06-25T13:03:39.209Z", "user": "<PERSON><PERSON><PERSON>", "action": "PROFILE_UPDATE", "details": "Perfil atualizado: pcRank"}, {"id": 1750856619216, "timestamp": "2025-06-25T13:03:39.216Z", "user": "<PERSON><PERSON><PERSON>", "action": "GAME_ADDED", "details": "Jo<PERSON> \"Cyberpunk 2077\" adicionado à biblioteca"}, {"id": 1750856619224, "timestamp": "2025-06-25T13:03:39.224Z", "user": "<PERSON><PERSON><PERSON>", "action": "PROFILE_UPDATE", "details": "Perfil atualizado: pcRank"}, {"id": 1750856619225, "timestamp": "2025-06-25T13:03:39.225Z", "user": "<PERSON><PERSON><PERSON>", "action": "PROFILE_UPDATE", "details": "Perfil atualizado: pcRank"}, {"id": 1750856619231, "timestamp": "2025-06-25T13:03:39.231Z", "user": "<PERSON><PERSON><PERSON>", "action": "USER_REGISTER", "details": "Novo usuário Erride se registrou no sistema"}, {"id": 1750856619232, "timestamp": "2025-06-25T13:03:39.232Z", "user": "<PERSON><PERSON><PERSON>", "action": "USER_REGISTER", "details": "Novo usuário Erride se registrou no sistema"}, {"id": 1750856619241, "timestamp": "2025-06-25T13:03:39.241Z", "user": "admin", "action": "GAME_ADDED", "details": "Jo<PERSON> \"Red Dead Redemption 2\" adicionado à biblioteca"}, {"id": 1750856619242, "timestamp": "2025-06-25T13:03:39.242Z", "user": "admin", "action": "GAME_ADDED", "details": "Jo<PERSON> \"Red Dead Redemption 2\" adicionado à biblioteca"}, {"id": 1750856619249, "timestamp": "2025-06-25T13:03:39.249Z", "user": "admin", "action": "ADMIN_LOGIN", "details": "Administrador fez login no sistema"}, {"id": 1750856619786, "timestamp": "2025-06-25T13:03:39.786Z", "user": "admin", "action": "GAME_COMPLETED", "details": "<PERSON><PERSON> \"<PERSON><PERSON>\" marcado como completado"}, {"id": 1750856619787, "timestamp": "2025-06-25T13:03:39.787Z", "user": "admin", "action": "GAME_COMPLETED", "details": "<PERSON><PERSON> \"<PERSON><PERSON>\" marcado como completado"}, {"id": 1750856619801, "timestamp": "2025-06-25T13:03:39.801Z", "user": "admin", "action": "GAME_ADDED", "details": "Jo<PERSON> \"Terraria\" adicionado à biblioteca"}, {"id": 1750856619802, "timestamp": "2025-06-25T13:03:39.802Z", "user": "admin", "action": "GAME_ADDED", "details": "Jo<PERSON> \"Terraria\" adicionado à biblioteca"}, {"id": 1750856619809, "timestamp": "2025-06-25T13:03:39.809Z", "user": "admin", "action": "ADMIN_LOGIN", "details": "Administrador fez login no sistema"}, {"id": 1750856619810, "timestamp": "2025-06-25T13:03:39.810Z", "user": "admin", "action": "ADMIN_LOGIN", "details": "Administrador fez login no sistema"}, {"id": 1750856619822, "timestamp": "2025-06-25T13:03:39.822Z", "user": "<PERSON><PERSON><PERSON>", "action": "PROFILE_UPDATE", "details": "Perfil atualizado: name, avatar, favoriteCompany, banner, pcSpecs, pcRank"}, {"id": 1750856619824, "timestamp": "2025-06-25T13:03:39.824Z", "user": "<PERSON><PERSON><PERSON>", "action": "PROFILE_UPDATE", "details": "Perfil atualizado: name, avatar, favoriteCompany, banner, pcSpecs, pcRank"}, {"id": 1750856619833, "timestamp": "2025-06-25T13:03:39.833Z", "user": "<PERSON><PERSON><PERSON>", "action": "GAME_ADDED", "details": "Jo<PERSON> \"Red Dead Redemption 2\" adicionado à biblioteca"}, {"id": 1750856619846, "timestamp": "2025-06-25T13:03:39.846Z", "user": "<PERSON><PERSON><PERSON>", "action": "PROFILE_UPDATE", "details": "Perfil atualizado: pcRank"}, {"id": 1750856619865, "timestamp": "2025-06-25T13:03:39.865Z", "user": "<PERSON><PERSON><PERSON>", "action": "GAME_REMOVED", "details": "Jo<PERSON> \"Cyberpunk 2077\" removido da biblioteca"}, {"id": 1750856619882, "timestamp": "2025-06-25T13:03:39.882Z", "user": "<PERSON><PERSON><PERSON>", "action": "PROFILE_UPDATE", "details": "Perfil atualizado: pcRank"}, {"id": 1750856619899, "timestamp": "2025-06-25T13:03:39.899Z", "user": "<PERSON><PERSON><PERSON>", "action": "GAME_ADDED", "details": "Jo<PERSON> \"Cyberpunk 2077\" adicionado à biblioteca"}, {"id": 1750856619915, "timestamp": "2025-06-25T13:03:39.915Z", "user": "<PERSON><PERSON><PERSON>", "action": "PROFILE_UPDATE", "details": "Perfil atualizado: pcRank"}, {"id": 1750856619928, "timestamp": "2025-06-25T13:03:39.928Z", "user": "<PERSON><PERSON><PERSON>", "action": "USER_REGISTER", "details": "Novo usuário Erride se registrou no sistema"}, {"id": 1750856619933, "timestamp": "2025-06-25T13:03:39.933Z", "user": "<PERSON><PERSON><PERSON>", "action": "USER_REGISTER", "details": "Novo usuário Erride se registrou no sistema"}, {"id": 1750856619935, "timestamp": "2025-06-25T13:03:39.935Z", "user": "admin", "action": "GAME_ADDED", "details": "Jo<PERSON> \"Red Dead Redemption 2\" adicionado à biblioteca"}, {"id": 1750856619944, "timestamp": "2025-06-25T13:03:39.944Z", "user": "admin", "action": "GAME_ADDED", "details": "Jo<PERSON> \"Red Dead Redemption 2\" adicionado à biblioteca"}, {"id": 1750856619951, "timestamp": "2025-06-25T13:03:39.951Z", "user": "admin", "action": "ADMIN_LOGIN", "details": "Administrador fez login no sistema"}, {"id": 1750856634881, "timestamp": "2025-06-25T13:03:54.881Z", "user": "admin", "action": "GAME_COMPLETED", "details": "<PERSON><PERSON> \"<PERSON><PERSON>\" marcado como completado"}, {"id": 1750856634890, "timestamp": "2025-06-25T13:03:54.890Z", "user": "admin", "action": "GAME_ADDED", "details": "Jo<PERSON> \"Terraria\" adicionado à biblioteca"}, {"id": 1750856634891, "timestamp": "2025-06-25T13:03:54.891Z", "user": "admin", "action": "GAME_ADDED", "details": "Jo<PERSON> \"Terraria\" adicionado à biblioteca"}, {"id": 1750856634899, "timestamp": "2025-06-25T13:03:54.899Z", "user": "admin", "action": "ADMIN_LOGIN", "details": "Administrador fez login no sistema"}, {"id": 1750856634908, "timestamp": "2025-06-25T13:03:54.908Z", "user": "<PERSON><PERSON><PERSON>", "action": "PROFILE_UPDATE", "details": "Perfil atualizado: name, avatar, favoriteCompany, banner, pcSpecs, pcRank"}, {"id": 1750856634915, "timestamp": "2025-06-25T13:03:54.915Z", "user": "<PERSON><PERSON><PERSON>", "action": "GAME_ADDED", "details": "Jo<PERSON> \"Red Dead Redemption 2\" adicionado à biblioteca"}, {"id": 1750856634916, "timestamp": "2025-06-25T13:03:54.916Z", "user": "<PERSON><PERSON><PERSON>", "action": "GAME_ADDED", "details": "Jo<PERSON> \"Red Dead Redemption 2\" adicionado à biblioteca"}, {"id": 1750856634924, "timestamp": "2025-06-25T13:03:54.924Z", "user": "<PERSON><PERSON><PERSON>", "action": "PROFILE_UPDATE", "details": "Perfil atualizado: pcRank"}, {"id": 1750856634932, "timestamp": "2025-06-25T13:03:54.932Z", "user": "<PERSON><PERSON><PERSON>", "action": "GAME_REMOVED", "details": "Jo<PERSON> \"Cyberpunk 2077\" removido da biblioteca"}, {"id": 1750856634934, "timestamp": "2025-06-25T13:03:54.934Z", "user": "<PERSON><PERSON><PERSON>", "action": "GAME_REMOVED", "details": "Jo<PERSON> \"Cyberpunk 2077\" removido da biblioteca"}, {"id": 1750856634952, "timestamp": "2025-06-25T13:03:54.952Z", "user": "<PERSON><PERSON><PERSON>", "action": "PROFILE_UPDATE", "details": "Perfil atualizado: pcRank"}, {"id": 1750856634960, "timestamp": "2025-06-25T13:03:54.960Z", "user": "<PERSON><PERSON><PERSON>", "action": "GAME_ADDED", "details": "Jo<PERSON> \"Cyberpunk 2077\" adicionado à biblioteca"}, {"id": 1750856634967, "timestamp": "2025-06-25T13:03:54.967Z", "user": "<PERSON><PERSON><PERSON>", "action": "PROFILE_UPDATE", "details": "Perfil atualizado: pcRank"}, {"id": 1750856634982, "timestamp": "2025-06-25T13:03:54.982Z", "user": "<PERSON><PERSON><PERSON>", "action": "USER_REGISTER", "details": "Novo usuário Erride se registrou no sistema"}, {"id": 1750856634996, "timestamp": "2025-06-25T13:03:54.996Z", "user": "admin", "action": "GAME_ADDED", "details": "Jo<PERSON> \"Red Dead Redemption 2\" adicionado à biblioteca"}, {"id": 1750856635006, "timestamp": "2025-06-25T13:03:55.006Z", "user": "admin", "action": "ADMIN_LOGIN", "details": "Administrador fez login no sistema"}, {"id": 1750856635007, "timestamp": "2025-06-25T13:03:55.007Z", "user": "admin", "action": "GAME_ADDED", "details": "Jo<PERSON> \"Red Dead Redemption 2\" adicionado à biblioteca"}, {"id": 1750856635016, "timestamp": "2025-06-25T13:03:55.016Z", "user": "admin", "action": "ADMIN_LOGIN", "details": "Administrador fez login no sistema"}]