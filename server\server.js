const express = require("express");
const cors = require("cors");
const fs = require("fs").promises;
const path = require("path");

const app = express();
const PORT = 3001;

// Middleware
app.use(cors());
app.use(express.json());

// Caminhos dos arquivos de dados
const DATA_DIR = path.join(__dirname, "data");
const USERS_FILE = path.join(DATA_DIR, "users.json");
const GAMES_FILE = path.join(DATA_DIR, "games.json");
const LOGS_FILE = path.join(DATA_DIR, "logs.json");

// Criar diretório de dados se não existir
async function ensureDataDir() {
  try {
    await fs.access(DATA_DIR);
  } catch {
    await fs.mkdir(DATA_DIR, { recursive: true });
  }
}

// Ler arquivo JSON
async function readJsonFile(filePath, defaultValue = []) {
  try {
    const data = await fs.readFile(filePath, "utf8");
    return JSON.parse(data);
  } catch {
    return defaultValue;
  }
}

// Escrever arquivo JSON
async function writeJsonFile(filePath, data) {
  await fs.writeFile(filePath, JSON.stringify(data, null, 2));
}

// Inicializar servidor
async function initServer() {
  await ensureDataDir();

  // Criar arquivos iniciais se não existirem
  const initialUsers = [];
  const initialGames = [];
  const initialLogs = [];

  if (
    !(await fs
      .access(USERS_FILE)
      .then(() => true)
      .catch(() => false))
  ) {
    await writeJsonFile(USERS_FILE, initialUsers);
  }

  if (
    !(await fs
      .access(GAMES_FILE)
      .then(() => true)
      .catch(() => false))
  ) {
    await writeJsonFile(GAMES_FILE, initialGames);
  }

  if (
    !(await fs
      .access(LOGS_FILE)
      .then(() => true)
      .catch(() => false))
  ) {
    await writeJsonFile(LOGS_FILE, initialLogs);
  }
}

// Rota raiz
app.get("/", (req, res) => {
  res.json({
    message: "🎮 Game Tracker Server",
    status: "online",
    endpoints: {
      health: "/api/health",
      users: "/api/users",
      games: "/api/games",
      logs: "/api/logs",
    },
    frontend: "http://localhost:5173",
  });
});

// Rotas de saúde
app.get("/api/health", (req, res) => {
  res.json({ status: "online", timestamp: new Date().toISOString() });
});

// Rotas para usuários
app.get("/api/users", async (req, res) => {
  try {
    const users = await readJsonFile(USERS_FILE);
    res.json(users);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.post("/api/users", async (req, res) => {
  try {
    const users = await readJsonFile(USERS_FILE);
    const newUser = { ...req.body, id: Date.now() };
    users.push(newUser);
    await writeJsonFile(USERS_FILE, users);
    res.json(newUser);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.put("/api/users/:id", async (req, res) => {
  try {
    const users = await readJsonFile(USERS_FILE);
    const userIndex = users.findIndex((u) => u.id == req.params.id);

    if (userIndex === -1) {
      return res.status(404).json({ error: "Usuário não encontrado" });
    }

    users[userIndex] = { ...users[userIndex], ...req.body };
    await writeJsonFile(USERS_FILE, users);
    res.json(users[userIndex]);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Rotas para jogos
app.get("/api/games", async (req, res) => {
  try {
    const games = await readJsonFile(GAMES_FILE);
    res.json(games);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.post("/api/games", async (req, res) => {
  try {
    const games = await readJsonFile(GAMES_FILE);
    const newGame = { ...req.body, id: Date.now() };
    games.push(newGame);
    await writeJsonFile(GAMES_FILE, games);
    res.json(newGame);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.put("/api/games/:id", async (req, res) => {
  try {
    const games = await readJsonFile(GAMES_FILE);
    const gameIndex = games.findIndex((g) => g.id == req.params.id);

    if (gameIndex === -1) {
      return res.status(404).json({ error: "Jogo não encontrado" });
    }

    games[gameIndex] = { ...games[gameIndex], ...req.body };
    await writeJsonFile(GAMES_FILE, games);
    res.json(games[gameIndex]);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.delete("/api/games/:id", async (req, res) => {
  try {
    const games = await readJsonFile(GAMES_FILE);
    const filteredGames = games.filter((g) => g.id != req.params.id);
    await writeJsonFile(GAMES_FILE, filteredGames);
    res.json({ success: true });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Rotas para logs
app.get("/api/logs", async (req, res) => {
  try {
    const logs = await readJsonFile(LOGS_FILE);
    res.json(logs);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.post("/api/logs", async (req, res) => {
  try {
    const logs = await readJsonFile(LOGS_FILE);
    const newLog = {
      ...req.body,
      id: Date.now(),
      timestamp: new Date().toISOString(),
    };
    logs.push(newLog);
    await writeJsonFile(LOGS_FILE, logs);
    res.json(newLog);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Rotas de sincronização
app.post("/api/sync/users", async (req, res) => {
  try {
    const existingUsers = await readJsonFile(USERS_FILE);
    const newUsers = req.body;

    // Mesclar usuários sem duplicar
    const mergedUsers = [...existingUsers];
    newUsers.forEach((newUser) => {
      if (!existingUsers.find((u) => u.username === newUser.username)) {
        mergedUsers.push(newUser);
      }
    });

    await writeJsonFile(USERS_FILE, mergedUsers);
    res.json({ synced: mergedUsers.length });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Inicializar servidor
initServer()
  .then(() => {
    app.listen(PORT, "0.0.0.0", () => {
      console.log(`🚀 Servidor rodando em http://localhost:${PORT}`);
      console.log(`📊 API disponível em http://localhost:${PORT}/api`);
      console.log(`💾 Dados salvos em: ${DATA_DIR}`);
    });
  })
  .catch(console.error);
