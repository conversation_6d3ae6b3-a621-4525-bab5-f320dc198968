{"name": "vovocheideodio", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --port 3000 --host 0.0.0.0", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"cors": "^2.8.5", "express": "^5.1.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.2"}, "devDependencies": {"@eslint/js": "^9.29.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.5.2", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "vite": "^7.0.0"}}